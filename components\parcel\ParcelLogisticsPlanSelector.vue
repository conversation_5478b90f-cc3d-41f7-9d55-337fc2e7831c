<template>
  <div class="shipping-selector-section">
    <div class="row items-center q-mb-sm">
      <q-icon name="flight_takeoff" color="primary" size="sm" class="q-mr-sm" />
      <div class="text-h6 text-weight-bold">物流方案</div>
    </div>
    <div class="text-caption text-grey-8 q-mb-md">请选择适合您的物流方案</div>

    <q-card flat bordered class="q-pa-md">
      <div class="row items-center">
        <div v-if="modelValue" class="col-grow">
          <div class="row items-center justify-between">
            <div>
              <div class="text-subtitle2 text-weight-medium">
                {{ modelValue.name }}
                <q-badge color="green" class="q-ml-sm">推荐</q-badge>
              </div>
              <div class="text-subtitle2">预计送达: {{ modelValue.transitTime }}天</div>
              <div class="text-caption text-grey-8">{{ modelValue.description }}</div>
            </div>
            <div class="text-right">
              <div class="text-body1 text-primary text-weight-medium">¥{{ fen2yuan(modelValue.total) }}</div>
              <q-btn flat round color="primary" icon="edit" @click="openDialog" />
            </div>
          </div>
        </div>
        <div v-else class="col-grow text-center">
          <q-btn color="primary" label="选择物流方案" @click="openDialog" />
        </div>
      </div>
    </q-card>

    <!-- 物流方案选择弹窗 -->
    <q-dialog v-model="shippingDialog" persistent :maximized="$q.screen.lt.md" transition-show="slide-up" transition-hide="slide-down">
      <q-card :style="$q.screen.lt.md ? '' : 'width: 1000px; max-width: 90vw'" class="shipping-dialog flex column">
        <q-card-section class="dialog-header row items-center justify-between">
          <div class="text-h6 text-weight-bold">
            <q-icon name="local_shipping" class="q-mr-sm" />
            选择物流方案
          </div>
          <q-btn icon="close" flat round dense v-close-popup color="white" class="close-btn" />
        </q-card-section>

        <q-card-section class="q-pt-none q-pt-lg dialog-content flex-1 overflow-auto">
          <!-- 使用统一的运费展示组件 -->
          <div class="shipping-results-container">
            <ShippingResults
              :results="shippingMethods"
              :loading="false"
              :selectable="true"
              :selected-option="tempSelectedShipping"
              display-mode="compact"
              :show-radio="false"
              @select-option="selectShipping" />
          </div>
        </q-card-section>

        <!-- 固定在底部的操作按钮 -->
        <div class="dialog-actions-fixed">
          <q-card-actions align="right" class="q-pa-md">
            <q-btn flat label="取消" color="grey" v-close-popup class="q-mr-sm" />
            <q-btn label="确认" color="primary" :disable="!tempSelectedShipping" @click="confirmShipping" />
          </q-card-actions>
        </div>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useQuasar } from 'quasar';
import { fen2yuan } from '../../utils/utils';
import ShippingResults from '~/components/shipping/ShippingResults.vue';
import { transformShippingQuoteResults } from '~/utils/shippingUtils';
import { useCategoryStore } from '~/store/category';

const $q = useQuasar();
const categoryStore = useCategoryStore();

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  shippingMethods: {
    type: Array,
    required: true,
    default: () => [],
  },
  parcelWeight: {
    type: [Number, String],
    required: true,
    default: '0.00',
  },
  selectedProducts: {
    type: Array,
    default: () => [],
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue', 'calculate-fee']);

// 弹窗状态
const shippingDialog = ref(false);
const tempSelectedShipping = ref(props.modelValue);

// 注意：展开状态管理现在由 ShippingResults 组件内部处理

// 转换后的物流方案数据（统一格式）
const transformedShippingMethods = computed(() => {
  // 将包裹API的物流方案数据转换为运费查询API的格式，然后使用统一的转换函数
  const convertedData = (props.shippingMethods || []).map((item) => ({
    // 基础信息
    id: item.id || item.priceId,
    priceId: item.priceId,
    productId: item.productId || item.id, // 包裹API中可能使用 id 作为 productId
    name: item.name,
    iconUrl: item.iconUrl,
    features: item.features || item.description,
    transitTime: item.transitTime || item.estimatedDelivery || '-',

    // 服务特性
    taxInclude: item.taxInclude || false,
    available: item.available !== false,
    unavailableReason: item.unavailableReason,
    recommended: item.recommended || false,
    freeInsure: item.freeInsure || false,
    deliveryRate: item.deliveryRate || 0.98,

    // 费用信息 - 统一使用 total 字段
    total: item.totalFee || item.total || 0,
    freight: item.freight || 0,
    customsFee: item.customsFee || 0,
    fuelFee: item.fuelFee || 0,
    registrationFee: item.registrationFee || 0,
    operationFee: item.operationFee || 0,
    serviceFee: item.serviceFee || 0,
    additionalFee: item.additionalFee || 0,

    // 重量信息
    weight: item.weight || 0,
    volumeWeight: item.volumeWeight || 0,
    chargeableWeight: item.chargeableWeight || 0,

    // 商品类型支持
    electronic: item.electronic || false,
    cosmetic: item.cosmetic || false,
    clothing: item.clothing || false,
    liquid: item.liquid || false,
    large: item.large || false,

    // 分类限制（如果有的话）
    categoryRestrictions: item.categoryRestrictions,
  }));

  // 使用统一的转换函数处理数据
  return transformShippingQuoteResults(convertedData, categoryStore.getCategoryNameById);
});

// 物流方案列表（后端已排序，直接使用）
const sortedShippingMethods = computed(() => {
  return transformedShippingMethods.value || [];
});

// 注意：格式化函数现在由 ShippingResults 组件内部处理

// 打开物流方案选择弹窗
function openDialog() {
  // 验证是否已选择商品
  if (!props.selectedProducts || props.selectedProducts.length === 0) {
    // 使用Quasar的通知组件提示用户
    $q.notify({
      color: 'warning',
      message: '请先选择商品再选择物流方案',
      icon: 'warning',
      position: 'top',
    });
    return;
  }

  tempSelectedShipping.value = props.modelValue;
  shippingDialog.value = true;
}

// 注意：选中状态检查现在由 ShippingResults 组件内部处理

// 选择物流方案
function selectShipping(shipping) {
  // shipping 参数是转换后的格式，需要找到对应的原始数据
  const originalPlan = props.shippingMethods.find((plan) => (plan.productId || plan.id) === shipping.productId && plan.priceId === shipping.priceId);

  tempSelectedShipping.value = originalPlan || shipping;
}

// 注意：详细信息获取现在由 ShippingResults 组件内部处理

// 确认物流方案选择
function confirmShipping() {
  emit('update:modelValue', tempSelectedShipping.value);
  shippingDialog.value = false;
}

// 监听父组件传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    tempSelectedShipping.value = newValue;
  },
  { immediate: true }
);

// 监听物流方案列表变化，自动选择第一个方案
watch(
  () => props.shippingMethods,
  (newMethods) => {
    if (!newMethods || newMethods.length === 0) {
      return;
    }

    // 如果当前没有选中的方案，自动选择第一个方案（后端已排序）
    if (!props.modelValue) {
      emit('update:modelValue', newMethods[0]);
    } else {
      // 如果已经有选中的方案，检查是否需要更新价格信息
      const currentSelected = props.modelValue;
      const currentSelectedId = currentSelected.productId || currentSelected.id;
      const updatedPlan = newMethods.find((plan) => (plan.productId || plan.id) === currentSelectedId && plan.priceId === currentSelected.priceId);

      // 如果找到了对应的方案且价格有变化，更新选中的方案
      if (updatedPlan && updatedPlan.totalFee !== currentSelected.totalFee) {
        console.log('物流方案价格已更新，从', currentSelected.totalFee, '到', updatedPlan.totalFee);
        emit('update:modelValue', updatedPlan);
      }
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.shipping-selector-section {
  .q-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }
}

.shipping-dialog {
  border-radius: 12px;
  overflow: hidden;

  .dialog-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;

    .text-h6 {
      color: white;
      margin: 0;
      font-size: 1.1rem;
    }

    .close-btn {
      color: white;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .dialog-content {
    max-height: 70vh;
    overflow-y: auto;

    .shipping-results-container {
      .shipping-results {
        .results-card {
          box-shadow: none;
          border: none;
        }
      }
    }
  }

  .q-card-actions {
    border-top: 1px solid #e0e0e0;
    background-color: #fafafa;

    .q-btn {
      min-width: 100px;
      font-weight: 500;
    }
  }
}

.shipping-methods-container {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 4px;
}

.shipping-method-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  background: white;
  overflow: hidden;

  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }

  &.selected {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
  }

  &.expanded {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  }
}

.shipping-basic-row {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f8fafc;
  }

  .shipping-method-item.selected & {
    background: #eff6ff;
  }
}

.selection-radio {
  flex-shrink: 0;
  margin-top: 2px;
}

.shipping-content {
  flex: 1;
  min-width: 0;
}

.shipping-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

.shipping-title {
  font-weight: 500;
  font-size: 15px;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.recommend-badge {
  font-size: 10px;
  padding: 2px 6px;
}

.shipping-price {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
  white-space: nowrap;
}

.expand-button {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.shipping-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.delivery-info {
  color: #6b7280;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;

  &.delivery-rate-info {
    min-width: 100px; // 固定妥投率位置，避免与其他标签重叠
  }
}

.service-tags {
  display: flex;
  gap: 4px;
}

.service-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.service-tag.tax-included {
  background: #dcfce7;
  color: #166534;
}

.service-tag.free-insurance {
  background: #dbeafe;
  color: #1e40af;
}

/* 详细信息展开区域样式 */
.shipping-details-expanded {
  background-color: #fafafa;
  border-top: 1px solid #e5e7eb;

  .details-content {
    background-color: #fff;
    margin: 0;
    border-radius: 0 0 8px 8px;
  }

  .fee-breakdown {
    .fee-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;

      &.total-fee {
        padding-top: 8px;
        font-size: 1.1rem;
      }
    }

    .fee-value {
      font-weight: 500;
      color: #1976d2;
    }
  }

  .weight-info {
    .weight-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
    }

    .weight-value {
      font-weight: 500;
      color: #1976d2;
    }
  }

  .features-content {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #1976d2;
  }

  .timeliness-chart {
    .delivery-rate {
      padding: 8px 12px;
      background-color: #e8f5e8;
      border-radius: 6px;
    }

    .timeliness-bars {
      .timeliness-bar-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 6px 0;

        .time-interval {
          min-width: 60px;
          font-size: 0.875rem;
          color: #666;
        }

        .progress-container {
          flex: 1;
        }

        .rate-value {
          min-width: 40px;
          text-align: right;
          font-weight: 500;
          color: #1976d2;
        }
      }
    }
  }
}

/* 弹窗样式优化 */
.shipping-dialog {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  max-height: 85vh;
  min-height: 400px;

  &:not(.q-dialog__inner--maximized) {
    max-width: 1000px;
    width: 90vw;
  }

  .dialog-header {
    flex-shrink: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;

    .close-btn {
      color: white !important;
    }
  }

  .dialog-content {
    flex: 1;
    padding: 0px;
    min-height: 0;
    max-height: calc(85vh - 160px); // 减去头部和底部按钮的高度
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

/* 固定底部按钮样式 */
.dialog-actions-fixed {
  flex-shrink: 0;
  background: white;
  border-top: 1px solid #e0e0e0;
  z-index: 100;
  border-radius: 0 0 12px 12px;

  .q-card-actions {
    margin: 0;
    padding: 16px 24px;

    .q-btn {
      min-width: 100px;
      height: 40px;
      font-weight: 500;

      &.q-btn--flat {
        border: 1px solid #e0e0e0;
      }
    }
  }
}

.q-card-section {
  padding: 16px 20px;
}

.q-card-actions {
  padding: 12px 20px 16px;
  gap: 8px;
}

/* 移动端适配 */
@media (max-width: 600px) {
  .shipping-dialog {
    // 移动端全屏显示
    &.q-dialog__inner--maximized {
      .dialog-header {
        padding: 12px 16px;

        .text-h6 {
          font-size: 1rem;
        }
      }

      .dialog-content {
        padding: 8px 16px;
        max-height: calc(100vh - 140px); // 移动端减去头部和底部的高度
      }

      .dialog-actions-fixed {
        border-radius: 0;

        .q-card-actions {
          padding: 12px 16px;

          .q-btn {
            min-width: 80px;
            height: 44px; // 移动端增加按钮高度
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>
