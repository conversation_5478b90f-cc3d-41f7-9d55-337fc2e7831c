<template>
  <div class="dashboard">
    <!-- 用户信息区域 -->
    <div class="user-info-card q-mb-md">
      <div class="row items-center q-pa-md">
        <div class="col-12 col-md-6">
          <div class="row items-center">
            <q-avatar size="80px" class="q-mr-md">
              <img v-if="userInfo.avatar" :src="userInfo.avatar" :alt="$t('accountDashboard.userAvatar')" />
              <q-icon v-else name="person" size="60px" color="grey-5" />
            </q-avatar>
            <div>
              <div class="text-h6 q-mb-xs">{{ userInfo.nickname || userInfo.name || $t('accountDashboard.respectedUser') }}</div>
              <div class="text-subtitle2 text-grey-7">{{ userInfo.email }}</div>
              <div class="q-mt-sm">
                <q-btn color="primary" outline size="sm" :label="$t('accountDashboard.editProfile')" to="/account/profile" class="q-mr-sm" />
                <!-- <q-btn color="primary" flat size="sm" :label="$t('accountDashboard.securitySettings')" to="/account/security" /> -->
              </div>
            </div>
          </div>
        </div>
        <div class="col-12 col-md-6">
          <div class="row q-col-gutter-md justify-end">
            <div class="col-6 col-sm-4 q-mt-md q-mt-md-none">
              <div class="text-center asset-box">
                <div class="text-h6 text-primary">{{ fen2yuan(userWallet.balance) }}</div>
                <div class="text-caption">{{ $t('accountDashboard.accountBalance') }}</div>
                <q-btn flat dense color="primary" :label="$t('accountDashboard.recharge')" to="/account/balance" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row q-col-gutter-md">
      <!-- 左侧：待处理任务和我的消息 -->
      <div class="col-12">
        <!-- 待处理任务 -->
        <q-card class="q-mb-md">
          <q-card-section class="bg-primary text-white compact-header">
            <div class="text-subtitle1 q-py-none">{{ $t('accountDashboard.pendingTasks') }}</div>
          </q-card-section>

          <q-card-section v-if="pendingTasks.length === 0" class="text-center q-py-lg">
            <q-icon name="check_circle" size="3em" color="positive" />
            <div class="q-mt-sm text-grey-7">{{ $t('accountDashboard.noPendingTasks') }}</div>
          </q-card-section>

          <q-list v-else bordered separator>
            <q-item v-for="(task, index) in pendingTasks" :key="index" clickable :to="task.link">
              <q-item-section avatar>
                <q-icon :name="task.icon" :color="task.color" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ task.title }}</q-item-label>
                <q-item-label caption>{{ task.description }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn flat round color="primary" icon="arrow_forward" :to="task.link" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card>

        <!-- 我的消息 -->
        <q-card class="q-mb-md">
          <q-card-section class="bg-primary text-white compact-header">
            <div class="row items-center justify-between q-py-none">
              <div class="text-subtitle1">{{ $t('accountDashboard.myMessages') }}</div>
              <q-btn flat round dense icon="more_horiz" color="white" size="sm">
                <q-menu>
                  <q-list style="min-width: 100px">
                    <q-item clickable v-close-popup @click="markAllRead">
                      <q-item-section>{{ $t('accountDashboard.markAllAsRead') }}</q-item-section>
                    </q-item>
                    <q-item clickable v-close-popup @click="clearAllMessages">
                      <q-item-section>{{ $t('accountDashboard.clearAllMessages') }}</q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>
              </q-btn>
            </div>
          </q-card-section>

          <q-card-section v-if="messages.length === 0" class="text-center q-py-lg">
            <q-icon name="mail" size="3em" color="grey-5" />
            <div class="q-mt-sm text-grey-7">{{ $t('accountDashboard.noMessages') }}</div>
          </q-card-section>

          <q-list v-else bordered separator>
            <q-item v-for="(msg, index) in messages" :key="index" clickable @click="readMessage(msg, index)">
              <q-item-section avatar>
                <q-icon name="mail" :color="msg.readStatus ? 'grey-5' : 'primary'" />
              </q-item-section>
              <q-item-section>
                <q-item-label :class="{ 'text-weight-bold': !msg.readStatus }">{{ msg.templateContent }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <div class="text-caption">{{ formatDateTime(msg.createTime) }}</div>
              </q-item-section>
            </q-item>
          </q-list>

          <q-card-actions align="center" v-if="messages.length > 0">
            <q-btn flat color="primary" :label="$t('accountDashboard.viewAll')" @click="navigateTo('/account/msg-inbox')" />
          </q-card-actions>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useUserStore } from '~/store/user';
import ParcelApi from '~/composables/parcelApi';
import { useI18n } from 'vue-i18n';
import NotifyApi from '~/composables/notifyApi';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

// 用户信息
const userStore = useUserStore();
const userInfo = ref({
  name: '',
  nickname: '',
  email: '',
  avatar: '',
  point: 0,
});
const userWallet = ref({
  balance: 0,
});
const numData = ref({
  unusedCouponCount: 0,
  orderCount: {
    allParcelCount: 0,
    unpaidParcelCount: 0,
    undeliveredParcelCount: 0,
    deliveredParcelCount: 0,
    unpaidOrderCount: 0,
    stockCount: 0,
  },
});

// 待处理任务
const pendingTasks = computed(() => {
  const tasks = [];

  // 待支付订单
  if (numData.value.orderCount?.unpaidOrderCount > 0) {
    tasks.push({
      icon: 'payment',
      color: 'negative',
      title: `${numData.value.orderCount.unpaidOrderCount}  ${t('accountDashboard.pendingTasksDesc.unpaidOrders')}`,
      description: t('accountDashboard.pendingTasksDesc.unpaidOrdersDesc'),
      link: '/account/orders?status=10',
    });
  }
  // 在库货物
  if (numData.value.orderCount?.stockCount > 0) {
    tasks.push({
      icon: 'warehouse',
      color: 'orange',
      title: `${numData.value.orderCount.stockCount}  在库货物`,
      description: '库存商品数量',
      link: '/account/stock',
    });
  }

  // 待支付包裹
  if (numData.value.orderCount?.unpaidCount > 0) {
    tasks.push({
      icon: 'payment',
      color: 'teal',
      title: `${numData.value.orderCount.unpaidParcelCount}  待支付包裹`,
      description: t('accountDashboard.pendingTasksDesc.unpaidOrdersDesc'),
      link: '/account/parcel?status=10',
    });
  }

  // 待收货包裹
  if (numData.value.orderCount?.undeliveredParcelCount > 0) {
    tasks.push({
      icon: 'local_shipping',
      color: 'primary',
      title: `${numData.value.orderCount.undeliveredParcelCount}  待收货包裹`,
      description: '待收货包裹',
      link: '/account/parcel?status=20',
    });
  }
  return tasks;
});

// 快捷入口
const { t } = useI18n();

// 消息
const messages = ref([]);

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([getUserInfo(), getWallet(), getNumData(), getMessages()]);
});

// 获取用户信息
async function getUserInfo() {
  try {
    await userStore.getUserInfo();
    userInfo.value = userStore.userInfo;
  } catch (error) {
    console.error(t('accountDashboard.errors.getUserInfoFailed'), error);
  }
}

// 获取钱包信息
async function getWallet() {
  try {
    await userStore.getWallet();
    userWallet.value = userStore.userWallet;
  } catch (error) {
    console.error(t('accountDashboard.errors.getWalletFailed'), error);
  }
}

// 获取数量数据
async function getNumData() {
  const { code, data } = await ParcelApi.getAllCount();
  if (code === 0) {
    numData.value.orderCount = data;
  }
}

// 获取消息
async function getMessages() {
  const { code, data } = await NotifyApi.getList({ size: 5 });
  if (code === 0) {
    messages.value = data;
  }
}

// 读取消息
function readMessage(_msg, index) {
  messages.value[index].read = true;
  // 这里应该调用标记消息为已读的API
}

// 全部标为已读
async function markAllRead() {
  await NotifyApi.updateAllRead();
  // 更新本地状态
  state.pagination.list.forEach((msg) => {
    msg.readStatus = true;
  });

  $q.notify({
    color: 'positive',
    message: '全部标记为已读成功',
    icon: 'check',
  });
}

// 清空消息
async function clearAllMessages() {
  const { code, data } = await NotifyApi.deleteAll();
  if (code === 0) {
    messages.value = [];
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  padding: 10px;
  background-color: #f5f7fa;
}

.user-info-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  }
}

.asset-box {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 12px;
  transition: transform 0.2s;

  &:hover {
    transform: translateY(-3px);
  }
}

.shortcut-btn {
  padding: 12px 0;
  border-radius: 8px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }
}

// 紧凑标题样式
.compact-header {
  padding: 8px 16px;
  min-height: 40px;

  .text-subtitle1 {
    font-weight: 500;
    line-height: 1.2;
  }
}

// 响应式调整
@media (max-width: 599px) {
  .user-info-card {
    .q-avatar {
      size: 60px;
    }
  }

  .compact-header {
    padding: 6px 12px;
    min-height: 36px;
  }
}
</style>
