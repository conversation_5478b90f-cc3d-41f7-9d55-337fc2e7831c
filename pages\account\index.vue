<template>
  <div class="dashboard">
    <!-- 用户信息区域 -->
    <div class="user-info-card q-mb-md">
      <div class="row items-center q-pa-md">
        <div class="col-12 col-md-6">
          <div class="row items-center">
            <q-avatar size="80px" class="q-mr-md">
              <img v-if="userInfo.avatar" :src="userInfo.avatar" :alt="$t('accountDashboard.userAvatar')" />
              <q-icon v-else name="person" size="60px" color="grey-5" />
            </q-avatar>
            <div>
              <div class="text-h6 q-mb-xs">{{ userInfo.nickname || userInfo.name || $t('accountDashboard.respectedUser') }}</div>
              <div class="text-subtitle2 text-grey-7">{{ userInfo.email }}</div>
              <div class="q-mt-sm">
                <q-btn color="primary" outline size="sm" :label="$t('accountDashboard.editProfile')" to="/account/profile" class="q-mr-sm" />
                <q-btn color="primary" flat size="sm" :label="$t('accountDashboard.securitySettings')" to="/account/security" />
              </div>
            </div>
          </div>
        </div>
        <div class="col-12 col-md-6">
          <div class="row q-col-gutter-md justify-end">
            <div class="col-6 col-sm-4 q-mt-md q-mt-md-none">
              <div class="text-center asset-box">
                <div class="text-h6 text-primary">{{ fen2yuan(userWallet.balance) }}</div>
                <div class="text-caption">{{ $t('accountDashboard.accountBalance') }}</div>
                <q-btn flat dense color="primary" :label="$t('accountDashboard.recharge')" to="/account/balance" />
              </div>
            </div>
            <!-- <div class="col-6 col-sm-4 q-mt-md q-mt-md-none">
              <div class="text-center asset-box">
                <div class="text-h6 text-orange">{{ userInfo.point }}</div>
                <div class="text-caption">{{ $t('accountDashboard.myPoints') }}</div>
                <q-btn flat dense color="orange" :label="$t('accountDashboard.view')" to="/account/points" />
              </div>
            </div>
            <div class="col-6 col-sm-4 q-mt-md q-mt-md-none">
              <div class="text-center asset-box">
                <div class="text-h6 text-teal">{{ numData.unusedCouponCount || 0 }}</div>
                <div class="text-caption">{{ $t('accountDashboard.availableCoupons') }}</div>
                <q-btn flat dense color="teal" :label="$t('accountDashboard.view')" to="/account/coupons" />
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="row q-col-gutter-md">
      <!-- 左侧：待处理任务和我的消息 -->
      <div class="col-12">
        <!-- 待处理任务 -->
        <q-card class="q-mb-md">
          <q-card-section class="bg-primary text-white compact-header">
            <div class="text-subtitle1 q-py-none">{{ $t('accountDashboard.pendingTasks') }}</div>
          </q-card-section>

          <q-card-section v-if="pendingTasks.length === 0" class="text-center q-py-lg">
            <q-icon name="check_circle" size="3em" color="positive" />
            <div class="q-mt-sm text-grey-7">{{ $t('accountDashboard.noPendingTasks') }}</div>
          </q-card-section>

          <q-list v-else bordered separator>
            <q-item v-for="(task, index) in pendingTasks" :key="index" clickable :to="task.link">
              <q-item-section avatar>
                <q-icon :name="task.icon" :color="task.color" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ task.title }}</q-item-label>
                <q-item-label caption>{{ task.description }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn flat round color="primary" icon="arrow_forward" :to="task.link" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card>

        <!-- 我的消息 -->
        <q-card class="q-mb-md">
          <q-card-section class="bg-primary text-white compact-header">
            <div class="row items-center justify-between q-py-none">
              <div class="text-subtitle1">{{ $t('accountDashboard.myMessages') }}</div>
              <q-btn flat round dense icon="more_horiz" color="white" size="sm">
                <q-menu>
                  <q-list style="min-width: 100px">
                    <q-item clickable v-close-popup @click="markAllRead">
                      <q-item-section>{{ $t('accountDashboard.markAllAsRead') }}</q-item-section>
                    </q-item>
                    <q-item clickable v-close-popup @click="clearAllMessages">
                      <q-item-section>{{ $t('accountDashboard.clearAllMessages') }}</q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>
              </q-btn>
            </div>
          </q-card-section>

          <q-card-section v-if="messages.length === 0" class="text-center q-py-lg">
            <q-icon name="mail" size="3em" color="grey-5" />
            <div class="q-mt-sm text-grey-7">{{ $t('accountDashboard.noMessages') }}</div>
          </q-card-section>

          <q-list v-else bordered separator>
            <q-item v-for="(msg, index) in messages" :key="index" clickable @click="readMessage(msg, index)">
              <q-item-section avatar>
                <q-icon name="mail" :color="msg.readStatus ? 'grey-5' : 'primary'" />
              </q-item-section>
              <q-item-section>
                <q-item-label :class="{ 'text-weight-bold': !msg.readStatus }">{{ msg.templateContent }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <div class="text-caption">{{ formatDate(msg.createTime) }}</div>
              </q-item-section>
            </q-item>
          </q-list>

          <q-card-actions align="center" v-if="messages.length > 0">
            <q-btn flat color="primary" :label="$t('accountDashboard.viewAll')" @click="navigateTo('/account/msg-inbox')" />
          </q-card-actions>
        </q-card>
      </div>

      <!-- 右侧：快捷入口和最近订单 -->
      <div class="col-12 col-lg-4">
        <!-- 快捷入口 -->
        <!-- <q-card class="q-mb-md">
          <q-card-section class="bg-primary text-white compact-header">
            <div class="text-subtitle1 q-py-none">{{ $t('accountDashboard.quickAccess') }}</div>
          </q-card-section>

          <q-card-section>
            <div class="row q-col-gutter-sm">
              <div class="col-4" v-for="(shortcut, index) in shortcuts" :key="index">
                <q-btn flat class="shortcut-btn full-width" :to="shortcut.link">
                  <div class="column items-center">
                    <q-icon :name="shortcut.icon" size="28px" :color="shortcut.color" />
                    <div class="q-mt-sm text-caption">{{ shortcut.title }}</div>
                  </div>
                </q-btn>
              </div>
            </div>
          </q-card-section>
        </q-card> -->

        <!-- 最近订单 -->
        <!-- <q-card class="q-mb-md">
          <q-card-section class="bg-primary text-white compact-header">
            <div class="text-subtitle1 q-py-none">{{ $t('accountDashboard.recentOrders') }}</div>
          </q-card-section>

          <q-card-section v-if="recentOrders.length === 0" class="text-center q-py-lg">
            <q-icon name="shopping_bag" size="3em" color="grey-5" />
            <div class="q-mt-sm text-grey-7">{{ $t('accountDashboard.noOrders') }}</div>
            <q-btn color="primary" outline class="q-mt-md" :label="$t('accountDashboard.goShopping')" to="/products" />
          </q-card-section>

          <q-list v-else bordered separator>
            <q-item v-for="(order, index) in recentOrders" :key="index" clickable :to="`/account/order-detail?id=${order.id}`">
              <q-item-section>
                <q-item-label>{{ $t('accountDashboard.orderNumber') }}{{ order.no }}</q-item-label>
                <q-item-label caption>{{ formatDate(order.createTime) }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-badge :color="getOrderStatusColor(order.status)">{{ getOrderStatusText(order.status) }}</q-badge>
              </q-item-section>
            </q-item>
          </q-list>

          <q-card-actions align="center" v-if="recentOrders.length > 0">
            <q-btn flat color="primary" :label="$t('accountDashboard.viewAllOrders')" to="/account/orders" />
          </q-card-actions>
        </q-card> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useUserStore } from '~/store/user';
import { fen2yuan } from '~/utils/utils';
import OrderApi from '~/composables/orderApi';
import ParcelApi from '~/composables/parcelApi';
import TransferApi from '~/composables/transferApi';
import CouponApi from '~/composables/couponApi';
import { useI18n } from 'vue-i18n';
import { navigateTo } from 'nuxt/app';
import NotifyApi from '~/composables/notifyApi';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

// 用户信息
const userStore = useUserStore();
const userInfo = ref({
  name: '',
  nickname: '',
  email: '',
  avatar: '',
  point: 0,
});
const userWallet = ref({
  balance: 0,
});
const numData = ref({
  unusedCouponCount: 0,
  orderCount: {
    // unpaidCount: 0,
    // undeliveredCount: 0,
    // deliveredCount: 0,
    // uncommentedCount: 0,
    allParcelCount: 0,
    unpaidParcelCount: 0,
    undeliveredParcelCount: 0,
    deliveredParcelCount: 0,
    unpaidOrderCount: 0,
    stockCount: 0,
  },
  // parcelCount: {
  //   unpaidCount: 0,
  //   unreceivedCount: 0,
  //   receivedCount: 0,
  //   allCount: 0,
  // },
});

// 待处理任务
const pendingTasks = computed(() => {
  const tasks = [];

  // 待支付订单
  if (numData.value.orderCount?.unpaidOrderCount > 0) {
    tasks.push({
      icon: 'payment',
      color: 'negative',
      title: `${numData.value.orderCount.unpaidOrderCount}  ${t('accountDashboard.pendingTasksDesc.unpaidOrders')}`,
      description: t('accountDashboard.pendingTasksDesc.unpaidOrdersDesc'),
      link: '/account/orders?status=10',
    });
  }
  // 在库货物
  if (numData.value.orderCount?.stockCount > 0) {
    tasks.push({
      icon: 'warehouse',
      color: 'orange',
      title: `${numData.value.orderCount.stockCount}  在库货物`,
      description: '库存商品数量',
      link: '/account/stock',
    });
  }

  // 待支付包裹
  if (numData.value.orderCount?.unpaidCount > 0) {
    tasks.push({
      icon: 'payment',
      color: 'teal',
      title: `${numData.value.orderCount.unpaidParcelCount}  待支付包裹`,
      description: t('accountDashboard.pendingTasksDesc.unpaidOrdersDesc'),
      link: '/account/parcel?status=10',
    });
  }

  // 待收货订单
  // if (numData.value.orderCount?.deliveredCount > 0) {
  //   tasks.push({
  //     icon: 'local_shipping',
  //     color: 'primary',
  //     title: `${numData.value.orderCount.deliveredCount}${t('accountDashboard.pendingTasksDesc.shippedOrders')}`,
  //     description: t('accountDashboard.pendingTasksDesc.shippedOrdersDesc'),
  //     link: '/account/orders?status=30',
  //   });
  // }

  // 待收货包裹
  if (numData.value.orderCount?.undeliveredParcelCount > 0) {
    tasks.push({
      icon: 'local_shipping',
      color: 'primary',
      title: `${numData.value.orderCount.undeliveredParcelCount}  ${t('accountDashboard.pendingTasksDesc.shippedParcels')}`,
      description: '待收货包裹',
      link: '/account/parcel?status=20',
    });
  }

  // 待评价订单
  // if (numData.value.orderCount?.uncommentedCount > 0) {
  //   tasks.push({
  //     icon: 'rate_review',
  //     color: 'orange',
  //     title: `${numData.value.orderCount.uncommentedCount}${t('accountDashboard.pendingTasksDesc.uncommentedOrders')}`,
  //     description: t('accountDashboard.pendingTasksDesc.uncommentedOrdersDesc'),
  //     link: '/account/orders?status=40',
  //   });
  // }

  // 待入库转运单
  // if (pendingTransfers.value.length > 0) {
  //   tasks.push({
  //     icon: 'inventory_2',
  //     color: 'teal',
  //     title: `${pendingTransfers.value.length}${t('accountDashboard.pendingTasksDesc.pendingTransfers')}`,
  //     description: t('accountDashboard.pendingTasksDesc.pendingTransfersDesc'),
  //     link: '/account/transfer',
  //   });
  // }

  return tasks;
});

// 快捷入口
const { t } = useI18n();
const shortcuts = [
  { title: t('accountDashboard.shortcuts.myOrders'), icon: 'shopping_bag', color: 'primary', link: '/account/orders' },
  { title: t('accountDashboard.shortcuts.myTransfers'), icon: 'local_shipping', color: 'teal', link: '/account/transfer' },
  { title: t('accountDashboard.shortcuts.shippingAddresses'), icon: 'location_on', color: 'orange', link: '/account/addresses' },
  { title: t('accountDashboard.shortcuts.myWishlist'), icon: 'favorite', color: 'pink', link: '/account/wishlist' },
  { title: t('accountDashboard.shortcuts.accountBalance'), icon: 'account_balance_wallet', color: 'purple', link: '/account/balance' },
  { title: t('accountDashboard.shortcuts.coupons'), icon: 'card_giftcard', color: 'deep-orange', link: '/account/coupons' },
  { title: t('accountDashboard.shortcuts.customerService'), icon: 'headset_mic', color: 'green', link: '/chat' },
];

// 消息
const messages = ref([]);

// 最近订单
const recentOrders = ref([]);

// 待处理转运单
const pendingTransfers = ref([]);

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([getUserInfo(), getWallet(), getNumData(), getRecentOrders(), getPendingTransfers(), getMessages()]);
});

// 获取用户信息
async function getUserInfo() {
  try {
    await userStore.getUserInfo();
    userInfo.value = userStore.userInfo;
  } catch (error) {
    console.error(t('accountDashboard.errors.getUserInfoFailed'), error);
  }
}

// 获取钱包信息
async function getWallet() {
  try {
    await userStore.getWallet();
    userWallet.value = userStore.userWallet;
  } catch (error) {
    console.error(t('accountDashboard.errors.getWalletFailed'), error);
  }
}

// 获取数量数据
async function getNumData() {
  try {
    // 获取订单数量
    // const { code, data } = await OrderApi.getOrderCount();
    const { code, data } = await ParcelApi.getAllCount();
    if (code === 0) {
      numData.value.orderCount = data;
    }

    // 获取未使用优惠券数量
    const couponRes = await CouponApi.getUnusedCouponCount();
    if (couponRes.code === 0) {
      numData.value.unusedCouponCount = couponRes.data;
    }
  } catch (error) {
    console.error(t('accountDashboard.errors.getNumDataFailed'), error);
  }
}

// 获取最近订单
async function getRecentOrders() {
  try {
    const { code, data } = await OrderApi.getOrderPage({
      pageNo: 1,
      pageSize: 3,
    });
    if (code === 0) {
      recentOrders.value = data.list;
    }
  } catch (error) {
    console.error(t('accountDashboard.errors.getRecentOrdersFailed'), error);
  }
}

// 获取待处理转运单
async function getPendingTransfers() {
  try {
    const { code, data } = await TransferApi.getTransferPage({
      pageNo: 1,
      pageSize: 10,
      status: 0, // 待入库状态
    });
    if (code === 0) {
      pendingTransfers.value = data.list;
    }
  } catch (error) {
    console.error(t('accountDashboard.errors.getPendingTransfersFailed'), error);
  }
}

// 获取消息
async function getMessages() {
  try {
    const { code, data } = await NotifyApi.getList();
    if (code === 0) {
      messages.value = data;
    }
    // 这里应该调用获取消息的API，暂时使用模拟数据
    // messages.value = [
    //   { id: 1, content: '您的订单 #************ 已发货', date: '2024-06-01 12:12:12', read: false },
    //   { id: 2, content: '端午节放假通知：6月8日至6月10日休息', date: '2024-05-30 10:30:00', read: false },
    //   { id: 3, content: '您有新的优惠券可领取', date: '2024-05-28 15:45:20', read: true },
    // ];
  } catch (error) {
    console.error(t('accountDashboard.errors.getMessagesFailed'), error);
  }
}

// 读取消息
function readMessage(_msg, index) {
  messages.value[index].read = true;
  // 这里应该调用标记消息为已读的API
}

// 全部标为已读
function markAllRead() {
  messages.value.forEach((msg) => {
    msg.read = true;
  });
  // 这里应该调用标记全部消息为已读的API
}

// 清空消息
function clearAllMessages() {
  messages.value = [];
  // 这里应该调用清空消息的API
}

// 格式化日期
function formatDate(dateStr) {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const now = new Date();
  const diff = now - date;

  // 今天内的消息显示时间
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  }

  // 一周内的消息显示星期几
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const weekdays = [
      t('accountDashboard.weekdays.sunday'),
      t('accountDashboard.weekdays.monday'),
      t('accountDashboard.weekdays.tuesday'),
      t('accountDashboard.weekdays.wednesday'),
      t('accountDashboard.weekdays.thursday'),
      t('accountDashboard.weekdays.friday'),
      t('accountDashboard.weekdays.saturday'),
    ];
    return weekdays[date.getDay()];
  }

  // 其他显示日期
  return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
}

// 获取订单状态文本
function getOrderStatusText(status) {
  switch (status) {
    case 0:
      return t('accountDashboard.orderStatus.pending');
    case 10:
      return t('accountDashboard.orderStatus.unpaid');
    case 20:
      return t('accountDashboard.orderStatus.unshipped');
    case 30:
      return t('accountDashboard.orderStatus.shipped');
    case 40:
      return t('accountDashboard.orderStatus.completed');
    case 50:
      return t('accountDashboard.orderStatus.closed');
    default:
      return t('accountDashboard.orderStatus.unknown');
  }
}

// 获取订单状态颜色
function getOrderStatusColor(status) {
  switch (status) {
    case 0:
      return 'grey';
    case 10:
      return 'orange';
    case 20:
      return 'blue';
    case 30:
      return 'purple';
    case 40:
      return 'positive';
    case 50:
      return 'negative';
    default:
      return 'grey';
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  padding: 20px;
  background-color: #f5f7fa;
}

.user-info-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  }
}

.asset-box {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 12px;
  transition: transform 0.2s;

  &:hover {
    transform: translateY(-3px);
  }
}

.shortcut-btn {
  padding: 12px 0;
  border-radius: 8px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }
}

// 紧凑标题样式
.compact-header {
  padding: 8px 16px;
  min-height: 40px;

  .text-subtitle1 {
    font-weight: 500;
    line-height: 1.2;
  }
}

// 响应式调整
@media (max-width: 599px) {
  .user-info-card {
    .q-avatar {
      size: 60px;
    }
  }

  .compact-header {
    padding: 6px 12px;
    min-height: 36px;
  }
}
</style>
