import { defineStore } from 'pinia';
import CategoryApi from '~/composables/categoryApi';

export const useCategoryStore = defineStore('category', {
  state: () => ({
    categories: [], // 分类列表
    categoryMap: new Map(), // 分类ID到名称的映射
    loading: false,
    initialized: false,
  }),

  getters: {
    getCategories: (state) => state.categories,
    getCategoryMap: (state) => state.categoryMap,
    isLoading: (state) => state.loading,
    isInitialized: (state) => state.initialized,
    
    // 根据ID获取分类名称
    getCategoryNameById: (state) => (categoryId) => {
      if (!categoryId) return '';
      return state.categoryMap.get(categoryId.toString()) || `分类${categoryId}`;
    },
  },

  actions: {
    async fetchCategories() {
      // 如果已经加载过数据，不重复加载
      if (this.initialized && this.categories.length > 0) {
        return this.categories;
      }

      try {
        this.loading = true;
        const response = await CategoryApi.getList();
        
        if (response.code === 0 && response.data) {
          this.categories = response.data;
          
          // 构建分类映射
          const map = new Map();
          response.data.forEach((category) => {
            map.set(category.id.toString(), category.name);
            
            // 如果有子分类，也添加到映射中
            if (category.children && Array.isArray(category.children)) {
              category.children.forEach((subCategory) => {
                map.set(subCategory.id.toString(), subCategory.name);
              });
            }
          });
          
          this.categoryMap = map;
          this.initialized = true;
          
          console.log('分类数据加载完成，共', this.categories.length, '个分类');
        }

        return this.categories;
      } catch (error) {
        console.error('Failed to fetch categories:', error);
        return [];
      } finally {
        this.loading = false;
      }
    },

    // 重置状态，强制重新加载数据
    resetState() {
      this.categories = [];
      this.categoryMap = new Map();
      this.initialized = false;
    },

    // 手动设置分类映射（用于测试或特殊情况）
    setCategoryMap(map) {
      this.categoryMap = map;
    },
  },
});
