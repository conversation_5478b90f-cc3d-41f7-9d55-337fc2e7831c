<template>
  <!-- 轮播活动 -->
  <div class="huodong" tabindex="-1">
    <!-- 桌面端轮播 -->
    <q-carousel
      v-model="slide"
      transition-prev="slide-right"
      transition-next="slide-left"
      swipeable
      animated
      arrows
      autoplay
      infinite
      height="140px"
      class="bg-grey-1 rounded-borders gt-xs desktop-carousel"
      tabindex="-1">
      <q-carousel-slide v-for="(group, index) in groupedActivities" :key="index" :name="index + 1" class="column no-wrap q-pa-none">
        <div class="row fit justify-between items-center">
          <div v-for="(activity, idx) in group" :key="idx" class="activity-item" @click="navigate(activity.link)">
            <q-img class="rounded-borders activity-image" :src="activity.imagepath" :alt="activity.title" />
            <div class="activity-text-overlay">
              <div class="activity-title">{{ activity.title }}</div>
              <div v-if="activity.subtitle" class="activity-subtitle">{{ activity.subtitle }}</div>
            </div>
          </div>
        </div>
      </q-carousel-slide>
    </q-carousel>

    <!-- 移动端轮播 -->
    <q-carousel
      v-model="mobileSlide"
      transition-prev="slide-right"
      transition-next="slide-left"
      swipeable
      animated
      arrows
      autoplay
      infinite
      height="180px"
      class="bg-grey-1 rounded-borders lt-sm mobile-carousel"
      tabindex="-1">
      <q-carousel-slide v-for="(activity, index) in props.items" :key="index" :name="index + 1" class="column no-wrap q-pa-none">
        <div class="row fit justify-center items-center q-py-none">
          <div class="mobile-activity-item" @click="navigate(activity.link)">
            <q-img class="rounded-borders mobile-activity-image" :src="activity.imagepath" :alt="activity.title" />
            <div class="mobile-activity-text-overlay">
              <div class="mobile-activity-title">{{ activity.title }}</div>
              <div v-if="activity.subtitle" class="mobile-activity-subtitle">{{ activity.subtitle }}</div>
            </div>
          </div>
        </div>
      </q-carousel-slide>
    </q-carousel>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps({
  // 活动列表
  items: {
    type: Array,
    required: false,
    default: () => [],
  },
});
const slide = ref(1);
const mobileSlide = ref(1);

// 将活动按每4个分组（桌面端）
const groupedActivities = computed(() => {
  const groups = [];
  for (let i = 0; i < props.items.length; i += 4) {
    groups.push(props.items.slice(i, i + 4));
  }
  return groups;
});

const navigate = (path) => {
  if (path && path !== '/') {
    router.push(path);
  }
};
</script>

<style lang="scss" scoped>
//轮播活动
.huodong {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  margin-top: 30px;
  overflow: hidden; /* 防止容器出现滚动条 */
  border-radius: 13px;

  .desktop-carousel {
    overflow: hidden !important; /* 防止出现滚动条 */
    outline: none !important; /* 防止获取焦点时出现轮廓线 */

    :deep(.q-carousel__slide) {
      overflow: hidden !important; /* 确保幻灯片内容不溢出 */
    }
  }

  .activity-item {
    width: 24%;
    height: 130px;
    position: relative;
    cursor: pointer;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.03);
    }
  }

  .activity-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
  }

  .activity-text-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: start;
    text-align: center;
    padding: 10px;
    padding-left: 40px;
    // border-radius: 8px;
  }

  .activity-title {
    color: white;
    font-size: 22px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    margin-bottom: 4px;
    line-height: 1.2;
  }

  .activity-subtitle {
    margin-top: 4px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    line-height: 1.2;
  }

  .mobile-carousel {
    overflow: hidden !important; /* 防止出现滚动条 */
    outline: none !important; /* 防止获取焦点时出现轮廓线 */

    :deep(.q-carousel__slide) {
      overflow: hidden !important; /* 确保幻灯片内容不溢出 */
    }
  }

  .mobile-activity-item {
    width: 90%;
    max-width: 400px;
    // height: 100px;
    position: relative;
    cursor: pointer;
  }

  .mobile-activity-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .mobile-activity-text-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: start;
    text-align: center;
    padding: 18px;
    padding-left: 40px;
    border-radius: 8px;
  }

  .mobile-activity-title {
    color: white;
    font-size: 22px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    margin-bottom: 2px;
    line-height: 1.2;
  }

  .mobile-activity-subtitle {
    margin-top: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 11px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    line-height: 1.2;
  }
}

@media (max-width: 599px) {
  .huodong {
    padding: 0;
    margin-top: 20px;
    overflow: hidden !important; /* 确保在移动端也不会出现滚动条 */

    .mobile-activity-image {
      // height: 100px; /* 小屏幕下稍微减小高度 */
    }
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .huodong {
    .activity-image {
      width: 23.5%;
      height: 120px; /* 平板设备上稍微调整高度 */
    }

    .desktop-carousel {
      height: 130px; /* 调整轮播容器高度 */
    }
  }
}
/* 全局样式，确保轮播组件不会出现滚动条 */
:deep(.q-carousel),
:deep(.q-carousel__slide),
:deep(.q-carousel__slides),
:deep(.q-carousel__slide > .q-carousel__slide-inner) {
  overflow: hidden !important;
  outline: none !important;
}
</style>
