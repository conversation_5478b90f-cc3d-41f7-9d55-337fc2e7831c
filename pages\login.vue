<template>
  <HeaderSimple />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />

  <div class="login-main">
    <div class="login-container">
      <div class="login-form">
        <div class="q-pa-md">
          <q-form ref="form" @submit="submitForm" class="q-gutter-y-md column" novalidate>
            <div class="login-form-title">{{ $t('title.login') }}</div>

            <q-input
              dense
              v-model="user.email"
              outlined
              clearable
              type="email"
              :label="$t('label.email')"
              :placeholder="$t('placeholder.email')"
              autocomplete="username"
              :error="!!$v.user.email.$error"
              :error-message="$v.user.email.$errors[0]?.$message">
              <template #prepend>
                <q-icon name="email" />
              </template>
            </q-input>

            <q-input
              dense
              v-model="user.password"
              outlined
              clearable
              :type="isPwd ? 'password' : 'text'"
              :label="$t('label.password')"
              :placeholder="$t('placeholder.password')"
              autocomplete="current-password"
              :error="!!$v.user.password.$error"
              :error-message="$v.user.password.$errors[0]?.$message">
              <template #prepend>
                <q-icon name="lock" />
              </template>
              <template #append>
                <q-icon :name="isPwd ? 'visibility_off' : 'visibility'" class="cursor-pointer" @click="isPwd = !isPwd" />
              </template>
            </q-input>

            <q-btn type="submit" color="primary" :label="$t('button.login')" />

            <div class="q-mt-sm row justify-between items-center">
              <q-checkbox dense v-model="remeber" :label="$t('label.rememberMe')" />
              <NuxtLink to="/recover" class="text-primary">{{ $t('label.forgotPassword') }}</NuxtLink>
            </div>

            <div class="policy-links text-center q-mt-sm">
              <div class="q-mt-xs">
                <span class="text-caption text-grey-7">{{ $t('text.loginPolicyText') || '登录即表示您同意我们的' }} &nbsp;</span>

                <NuxtLink to="/agreement" class="text-primary text-caption">{{ $t('label.userAgreement') }}</NuxtLink>
                <span class="q-mx-xs text-caption">|</span>
                <NuxtLink to="/privacy" class="text-primary text-caption">{{ $t('label.privatePolicy') }}</NuxtLink>
              </div>
            </div>

            <div class="text-subtitle2 text-secondary q-mb-xs text-center">{{ $t('label.loginWithThird') }}</div>
            <div class="row justify-center q-mt-none">
              <q-btn flat round color="blue" icon="facebook" />
              <q-btn flat round color="teal" icon="person" />
              <q-btn flat round color="green" icon="wechat" />
            </div>

            <q-separator />
            <div class="text-subtitle2 text-center">
              {{ $t('label.noAccount') }} &nbsp;&nbsp;<a href="/register" class="text-primary"> {{ $t('label.registerNow') }}</a>
            </div>
          </q-form>
        </div>
      </div>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import { useState, useRouter, useRoute } from '#imports';
import useVuelidate from '@vuelidate/core';
import { createValidators } from '~/utils/i18n-validators';
import AuthApi from '~/composables/authApi';
import { useUserStore } from '~/store/user';
import { useI18n } from 'vue-i18n';
import { useAdTracking } from '~/composables/useAdTracking';

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const breadcrumbs = [{ label: t('title.login'), to: '/login' }];

// 广告追踪功能
const { handleUserLogin } = useAdTracking();
const user = ref({
  email: '',
  password: '',
});
const isPwd = ref(true);
const remeber = ref(true);

// 使用 Vuelidate 定义验证规则
const validators = createValidators(useNuxtApp().$t); // 动态生成验证规则
const rules = {
  user: {
    email: { required: validators.required, email: validators.email }, // 必填且为邮箱格式
    password: { required: validators.required, minLength: validators.minLength(6), maxLength: validators.maxLength(20) }, // 必填且至少6位
  },
};
const $v = useVuelidate(rules, { user });

const submitForm = async () => {
  $v.value.$touch(); // 触发验证
  if ($v.value.$invalid) {
    console.log('Form validation failed');
    return;
  }

  const { code } = await AuthApi.loginEmail({ email: user.value.email, password: user.value.password }, t);

  if (code === 0) {
    //获取用户信息
    await useUserStore().getUserInfo();

    // 处理广告追踪登录
    try {
      const userInfo = useUserStore().userInfo;
      if (userInfo?.id) {
        await handleUserLogin(userInfo.id);
      }
    } catch (error) {
      console.error('处理登录广告追踪失败:', error);
    }

    // 登录成功后跳转
    const returnTo = route.query.returnTo;
    const redirectToUrl = returnTo && returnTo !== '/login' ? returnTo : '/';
    await navigateTo(redirectToUrl, { replace: true }); // replace 避免回退到登录页
  }
};
</script>

<style lang="scss" scoped>
.login-main {
  max-width: 1200px;
  width: 100%;
  min-height: 500px;
  margin: 50px auto;
  background-image: url('/public/images/login.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 30px;
}

.login-form {
  width: 360px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 10px;
  transition: all 0.3s ease;

  .login-form-title {
    font-size: 1.5rem;
    font-weight: 500;
    text-align: center;
    margin-top: 20px;
    margin-bottom: 20px;
    color: #1976d2;
    position: relative;

    &:after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 3px;
      background-color: #1976d2;
      border-radius: 2px;
    }
  }

  .q-btn[type='submit'] {
    margin-top: 10px;
    height: 42px;
    font-weight: 500;
  }
}

/* 平板电脑样式 */
@media (max-width: 1023px) {
  .login-main {
    max-width: 90%;
    margin: 30px auto;
    background-position: left center;
  }

  .login-container {
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
  }

  .login-form {
    width: 400px;
    max-width: 100%;
  }
}

/* 手机样式 */
@media (max-width: 599px) {
  .login-main {
    max-width: 95%;
    margin: 20px auto;
    background-image: none;
    background-color: #f5f5f5;
    min-height: auto;
    box-shadow: none;
  }

  .login-container {
    padding: 15px;
  }

  .login-form {
    width: 100%;
    box-shadow: none;

    .login-form-title {
      font-size: 1.3rem;
      margin-top: 10px;
      margin-bottom: 15px;
    }
  }
}
</style>
