# 用户信息页面测试清单

## 功能测试

### 1. 基本信息编辑
- [ ] 用户名字段：必填验证、长度验证（2-20字符）
- [ ] 昵称字段：长度验证（最多30字符）
- [ ] 性别字段：必填验证、下拉选择
- [ ] 生日字段：必填验证、日期选择器
- [ ] 头像上传：使用SingleImageUpload组件，支持预览和删除
- [ ] 订阅开关：控制邮件通知订阅状态

### 2. 联系方式编辑
- [ ] 邮箱显示：显示验证状态，支持重发验证邮件
- [ ] 手机号编辑：国家区号+手机号格式，无需验证码

### 3. 数据提交
- [ ] 基本信息保存：调用正确的API接口
- [ ] 联系方式保存：更新手机号格式
- [ ] 错误处理：显示适当的错误消息
- [ ] 成功提示：保存成功后显示通知

### 4. 表单验证
- [ ] 实时验证：输入时显示验证错误
- [ ] 提交验证：提交前进行完整验证
- [ ] 国际化：验证消息支持多语言

## 样式测试

### 1. 桌面端
- [ ] 卡片样式：圆角、阴影、边框
- [ ] 输入框：高度42px，圆角8px
- [ ] 按钮：高度40px，圆角8px
- [ ] 间距：表单组间距20px

### 2. 移动端
- [ ] 响应式布局：小屏幕适配
- [ ] 输入框：高度38px
- [ ] 按钮：高度36px
- [ ] 间距：适当缩小

### 3. 颜色和字体
- [ ] 标题：#2c3e50，字重600
- [ ] 标签：#34495e，字重500
- [ ] 内容：#2c3e50，正常字重

## API对接测试

### 1. 数据结构
- [ ] name: 用户名
- [ ] nickname: 昵称
- [ ] sex: 性别（0保密，1男，2女）
- [ ] avatar: 头像URL
- [ ] mobile: 手机号（区号+空格+号码）
- [ ] birthday: 生日（YYYY-MM-DD格式）
- [ ] subscribed: 是否订阅（boolean）

### 2. API调用
- [ ] PUT /member/user/update：更新用户信息
- [ ] 请求体包含所有必需字段
- [ ] 响应处理正确

## 用户体验测试

### 1. 交互流程
- [ ] 编辑按钮：切换到编辑模式
- [ ] 取消按钮：放弃更改
- [ ] 保存按钮：提交更改
- [ ] 加载状态：显示保存中状态

### 2. 错误处理
- [ ] 网络错误：显示重试提示
- [ ] 验证错误：高亮错误字段
- [ ] 服务器错误：显示错误消息

### 3. 成功反馈
- [ ] 保存成功：显示成功通知
- [ ] 数据更新：页面数据实时更新
- [ ] 状态重置：退出编辑模式

## 兼容性测试

### 1. 浏览器兼容
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge

### 2. 设备兼容
- [ ] 桌面端
- [ ] 平板端
- [ ] 手机端

### 3. 语言兼容
- [ ] 中文界面
- [ ] 英文界面
- [ ] 其他语言界面

## 性能测试

### 1. 加载性能
- [ ] 页面初始加载时间
- [ ] 组件渲染时间
- [ ] 图片上传响应时间

### 2. 内存使用
- [ ] 无内存泄漏
- [ ] 组件正确销毁
- [ ] 事件监听器清理

## 安全测试

### 1. 输入验证
- [ ] XSS防护
- [ ] 输入长度限制
- [ ] 特殊字符处理

### 2. 文件上传
- [ ] 文件类型验证
- [ ] 文件大小限制
- [ ] 恶意文件检测
