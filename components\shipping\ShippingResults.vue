<template>
  <div class="shipping-results">
    <q-card class="results-card" :class="{ 'compact-mode': displayMode === 'compact' }">
      <q-card-section class="q-pb-none">
        <div class="row items-center justify-between">
          <h3 class="text-h6 q-ma-none">
            <q-icon name="local_shipping" class="q-mr-sm" />
            {{ t('shippingCalculator.results', '运输方案') }}
          </h3>
          <q-chip v-if="results.length > 0" color="positive" text-color="white" icon="check_circle" :label="`${results.length} ${t('shippingCalculator.common.options', '个方案')}`" />
        </div>
      </q-card-section>

      <q-card-section>
        <!-- 加载状态 -->
        <div v-if="loading" class="text-center q-py-xl">
          <q-spinner-dots size="50px" color="primary" />
          <div class="text-subtitle1 q-mt-md">{{ t('shippingCalculator.common.calculating', '计算中...') }}</div>
        </div>

        <!-- 无结果 -->
        <div v-else-if="results.length === 0" class="text-center q-py-xl">
          <q-icon name="search_off" size="60px" color="grey-5" />
          <div class="text-subtitle1 text-grey-7 q-mt-md">
            {{ t('shippingCalculator.noResults', '暂无可用的运输方案') }}
          </div>
        </div>

        <!-- 结果列表 -->
        <div v-else class="results-list">
          <div v-for="(result, index) in results" :key="result.id" class="shipping-result-item q-mb-md" :class="{ unavailable: !result.available, selected: selectable && isSelected(result) }">
            <!-- 移动端紧凑布局 -->
            <q-card
              v-if="$q.screen.lt.md && displayMode === 'compact'"
              flat
              bordered
              class="shipping-option-card mobile-compact-card"
              :class="{ 'unavailable-card': !result.available, 'selected-card': selectable && isSelected(result) }"
              @click="selectable && result.available ? selectOption(result) : null"
              :style="{ cursor: selectable && result.available ? 'pointer' : 'default' }">
              <!-- 推荐标志 - 右上角角标显示 -->
              <div v-if="result.recommended" class="recommended-badge">
                <span class="recommended-text">{{ t('shippingCalculator.common.recommend') }}</span>
              </div>

              <q-card-section class="compact-card-content">
                <div class="row items-center no-wrap">
                  <!-- 选择框 -->
                  <div v-if="selectable && result.available && showRadio" class="col-auto q-mr-sm">
                    <q-radio :model-value="selectedOption" :val="result" @update:model-value="selectOption" color="primary" />
                  </div>

                  <!-- 头像 -->
                  <div class="col-auto q-mr-sm">
                    <q-avatar size="40px" color="primary" text-color="white">
                      <img v-if="result.iconUrl" :src="result.iconUrl" />
                      <span v-else>{{ (result.name || 'L').charAt(0).toUpperCase() }}</span>
                    </q-avatar>
                  </div>

                  <!-- 主要信息 -->
                  <div class="col">
                    <div class="shipping-name text-weight-medium">{{ result.name }}</div>
                    <div class="shipping-meta text-caption text-grey-7">
                      <span>{{ result.transitTime }} {{ t('shippingCalculator.common.days') }}</span>
                      <span v-if="result.deliveryRate" class="q-ml-sm">{{ t('shippingCalculator.deliveryRate') }} {{ Math.round(result.deliveryRate * 100) }}%</span>
                      <q-badge v-if="result.taxInclude" color="green" text-color="white" :label="t('shippingCalculator.taxIncluded')" class="q-ml-xs" />
                    </div>
                  </div>

                  <!-- 价格和展开按钮 -->
                  <div class="col-auto text-right">
                    <div class="shipping-price text-weight-bold text-primary" v-show="result.available">¥{{ (result.total / 100).toFixed(2) }}</div>
                    <q-btn v-show="result.available" flat round color="primary" :icon="expandedItem === result.id ? 'expand_less' : 'expand_more'" size="sm" @click.stop="toggleExpand(result.id)">
                    </q-btn>
                  </div>
                </div>
              </q-card-section>

              <!-- 详细信息展开区域 -->
              <q-slide-transition>
                <div v-show="result.available && expandedItem === result.id" class="shipping-details-expanded">
                  <q-separator />
                  <q-card-section class="q-pt-md">
                    <div class="mobile-details">
                      <!-- 费用详情 -->
                      <div class="detail-section">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="credit_card" class="q-mr-xs" />
                          {{ t('shippingCalculator.feeBreakdown', '费用详情') }}
                        </h6>
                        <div class="fee-breakdown">
                          <div class="fee-item">
                            <span>{{ t('shippingCalculator.freight', '基础运费') }}:</span>
                            <span class="fee-value">{{ (getBaseFee(result) / 100).toFixed(2) }}</span>
                          </div>
                          <div class="fee-item" v-if="result.registrationFee > 0">
                            <span>{{ t('shippingCalculator.registrationFee', '挂号费') }}:</span>
                            <span class="fee-value">{{ (result.registrationFee / 100).toFixed(2) }}</span>
                          </div>
                          <div class="fee-item">
                            <span>{{ t('shippingCalculator.operationFee', '操作费') }}:</span>
                            <span class="fee-value">{{ (result.operationFee / 100).toFixed(2) }}</span>
                          </div>
                          <div class="fee-item">
                            <span>{{ t('shippingCalculator.customsFee', '清关费') }}:</span>
                            <span class="fee-value">{{ (result.customsFee / 100).toFixed(2) }}</span>
                          </div>
                          <div class="fee-item">
                            <span>{{ t('shippingCalculator.fuelFee', '燃油费') }}:</span>
                            <span class="fee-value">{{ (result.fuelFee / 100).toFixed(2) }}</span>
                          </div>
                          <div class="fee-item">
                            <span>{{ t('shippingCalculator.serviceFee', '服务费') }}:</span>
                            <span class="fee-value">{{ (result.serviceFee / 100).toFixed(2) }}</span>
                          </div>
                          <div class="fee-item">
                            <span>{{ t('shippingCalculator.additionalFee', '附加费') }}:</span>
                            <span class="fee-value">{{ (result.additionalFee / 100).toFixed(2) }}</span>
                          </div>
                          <div class="fee-item total-fee">
                            <span class="text-weight-bold">{{ t('shippingCalculator.totalFee', '总费用') }}:</span>
                            <span class="fee-value text-weight-bold text-primary">{{ (result.total / 100).toFixed(2) }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- 重量信息 -->
                      <div class="detail-section">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="scale" class="q-mr-xs" />
                          {{ t('shippingCalculator.weightInfo', '重量信息') }}
                        </h6>
                        <div class="weight-info">
                          <div class="weight-item">
                            <span>{{ t('shippingCalculator.actualWeight', '实际重量') }}:</span>
                            <span class="weight-value">{{ result.weight || 0 }}g</span>
                          </div>
                          <div class="weight-item">
                            <span>{{ t('shippingCalculator.volumeWeight', '体积重量') }}:</span>
                            <span class="text-caption text-grey-7"
                              >{{ `${t('shippingCalculator.common.l')} × ${t('shippingCalculator.common.w')} × ${t('shippingCalculator.common.h')}` }} /
                              {{ result.volumeBase }}
                            </span>
                            <span class="weight-value">{{ result.volumeWeight || 0 }}g</span>
                          </div>
                          <div class="weight-item">
                            <span class="text-weight-bold">{{ t('shippingCalculator.chargeableWeight') }}:</span>
                            <span class="weight-value text-weight-bold text-primary">{{ result.chargeableWeight || 0 }}g</span>
                          </div>
                        </div>
                      </div>

                      <!-- 包裹限制 -->
                      <div class="detail-section">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="straighten" class="q-mr-xs" />
                          {{ t('shippingCalculator.packageLimit', '包裹限制') }}
                        </h6>
                        <div class="weight-info">
                          <div class="weight-item">
                            <span>{{ t('shippingCalculator.weightLimit') }}:</span>
                            <span class="weight-value">{{ result?.minWeight || 0 }} - {{ result?.maxWeight || 0 }}g</span>
                          </div>
                          <div class="weight-item">
                            <span>{{ t('shippingCalculator.sizeLimit') }}:</span>
                            <span class="weight-value">{{ formatSizeLimitM(result.sizeRestrictions) }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- 分类限制 -->
                      <div class="detail-section" v-if="result.categoryRestrictions && result.categoryRestrictions.length > 0">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="category" class="q-mr-xs" />
                          {{ t('shippingCalculator.categoryRestrictions') }}
                        </h6>
                        <div class="category-restrictions-mobile">
                          <div v-for="category in result.categoryRestrictions" :key="category.name" class="restriction-item">
                            <div class="restriction-category text-weight-medium q-mb-xs">{{ category.name }}</div>
                            <div v-if="getAllowList(category) && getAllowList(category).length > 0" class="restriction-section">
                              <div class="restriction-tags">
                                <span class="restriction-label text-positive text-caption text-weight-bold">{{ t('shippingCalculator.allowedItems') }}: </span>
                                <!-- <q-chip v-for="item in getAllowList(category)" :key="item" size="xs" color="positive" text-color="white"> -->
                                <!-- {{ item }} -->
                                <!-- </q-chip> -->
                                <span size="xs" class="text-positive text-caption">
                                  {{ getAllowList(category).join(', ') }}
                                </span>
                              </div>
                            </div>

                            <div v-if="getBlockList(category) && getBlockList(category).length > 0" class="restriction-section">
                              <div class="restriction-tags">
                                <span class="restriction-label text-negative text-caption text-weight-bold">{{ t('shippingCalculator.blockedItems') }}: </span>
                                <!-- <q-chip v-for="item in getBlockList(category)" :key="item" size="sm" color="negative" text-color="white" class="q-ma-xs">
                                  {{ item }}
                                </q-chip> -->
                                <span size="xs" class="text-negative text-caption">
                                  {{ getBlockList(category).join(', ') }}
                                </span>
                              </div>
                            </div>
                            <q-separator />
                          </div>
                        </div>
                      </div>

                      <!-- 服务特性 -->
                      <div class="detail-section" v-if="result.features">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="star" class="q-mr-xs" />
                          {{ t('shippingCalculator.features') }}
                        </h6>
                        <div class="features-content">
                          {{ result.features }}
                        </div>
                      </div>
                    </div>
                  </q-card-section>
                </div>
              </q-slide-transition>
            </q-card>

            <!-- 桌面端和非紧凑模式的原有布局 -->
            <q-card
              v-else
              flat
              bordered
              class="shipping-option-card"
              :class="{ 'unavailable-card': !result.available, 'selected-card': selectable && isSelected(result) }"
              @click="selectable && result.available ? selectOption(result) : null"
              :style="{ cursor: selectable && result.available ? 'pointer' : 'default' }">
              <!-- 推荐标志 - 右上角角标显示 -->
              <div v-if="result.recommended" class="recommended-badge">
                <span class="recommended-text">{{ t('shippingCalculator.common.recommend') }}</span>
              </div>

              <!-- 基础信息行 -->
              <q-item class="shipping-option-header" :class="{ clickable: selectable && result.available }">
                <!-- 选择模式下的单选框 -->
                <q-item-section v-if="selectable && result.available && showRadio" side>
                  <q-radio :model-value="selectedOption" :val="result" @update:model-value="selectOption" color="primary" />
                </q-item-section>

                <q-item-section avatar>
                  <q-avatar size="50px" color="primary" text-color="white">
                    <!-- 优先显示图片 兜底：显示图标或首字母 -->
                    <img v-if="result.iconUrl" :src="result.iconUrl" />
                    <span v-else>{{ (result.name || 'L').charAt(0).toUpperCase() }}</span>
                  </q-avatar>
                </q-item-section>

                <q-item-section>
                  <q-item-label class="text-weight-medium text-subtitle1">
                    {{ result.name }}
                  </q-item-label>

                  <!-- 基础运输信息 -->
                  <div class="shipping-basic-info q-mt-sm">
                    <div class="row q-gutter-md">
                      <div class="shipping-info-item">
                        <q-icon name="schedule" size="sm" class="q-mr-xs" />
                        <span class="text-caption"> {{ t('shippingCalculator.transitTime', '预计') }}: {{ result.transitTime }} {{ t('shippingCalculator.common.days', '天') }} </span>
                        <q-icon name="help_outline" size="18px">
                          <q-tooltip anchor="center right" self="center left" :offset="[10, 10]">
                            <div style="max-width: 300px; white-space: normal">
                              {{ t('shippingCalculator.transitTimeTip') }}
                            </div>
                          </q-tooltip>
                        </q-icon>
                      </div>
                      <div class="shipping-info-item" v-if="result.deliveryRate">
                        <q-icon name="verified" size="sm" class="q-mr-xs" />
                        <span class="text-caption"> {{ t('shippingCalculator.deliveryRate') }}: {{ result.deliveryRate * 100 }}% </span>
                      </div>
                      <div class="shipping-info-item" v-if="result.taxInclude">
                        <q-icon name="security" size="sm" class="q-mr-xs" />
                        <span class="text-caption">
                          {{ t('shippingCalculator.taxIncluded') }}
                        </span>
                      </div>
                      <div class="shipping-info-item">
                        <q-badge v-if="result.electronic" rounded color="red" :label="t('shippingCalculator.common.battery')" />
                        <q-badge v-if="result.cosmetic" rounded color="green" :label="t('shippingCalculator.common.beauty')" />
                        <q-badge v-if="result.clothing" rounded color="red" :label="t('shippingCalculator.common.clothing')" />
                        <q-badge v-if="result.liquid" rounded color="red" :label="t('shippingCalculator.common.liquid')" />
                        <q-badge v-if="result.large" rounded color="red" :label="t('shippingCalculator.common.heavy')" />
                      </div>
                    </div>
                  </div>
                </q-item-section>
                <q-item-section>
                  <div class="text-left text-caption text-grey-7">
                    {{ result.features }}
                  </div>
                </q-item-section>

                <q-item-section side>
                  <div class="text-right">
                    <!-- 可用方案显示价格 -->
                    <div v-if="result.available" class="price-display">
                      <span class="price-amount">{{ fen2yuan(result.total) }}</span>
                    </div>
                    <!-- 不可用方案显示原因 -->
                    <div v-else class="unavailable-reason">
                      <q-icon name="block" color="negative" size="sm" class="q-mr-xs" />
                      <span class="text-negative text-weight-medium">{{ t('shippingCalculator.unavailable') }}</span>
                    </div>
                    <div v-else class="text-caption text-negative">
                      {{ result.unavailableReason || t('shippingCalculator.unavailable') }}
                    </div>
                  </div>
                </q-item-section>

                <q-item-section side>
                  <div class="action-buttons">
                    <q-btn v-if="result.available" flat round color="primary" :icon="expandedItem === result.id ? 'expand_less' : 'expand_more'" size="sm" @click.stop="toggleExpand(result.id)">
                      <q-tooltip>{{ expandedItem === result.id ? t('shippingCalculator.common.collapse') : t('shippingCalculator.common.expand') }}</q-tooltip>
                    </q-btn>
                    <!-- 不可用方案不显示展开按钮 -->
                    <div v-else class="unavailable-placeholder"></div>
                  </div>
                </q-item-section>
              </q-item>

              <!-- 详细信息展开区域 - 只有可用方案才能展开 -->
              <q-slide-transition>
                <div v-show="result.available && expandedItem === result.id" class="shipping-details-expanded">
                  <q-separator />
                  <q-card-section class="q-pt-md">
                    <div class="row q-col-gutter-md">
                      <!-- 重量信息 -->
                      <div class="col-12 col-md-6">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="scale" class="q-mr-xs" />
                          {{ t('shippingCalculator.weightInfo', '重量信息') }}
                        </h6>
                        <q-list dense>
                          <q-item>
                            <q-item-section>
                              <div class="weight-info">
                                <div class="weight-item">
                                  <span>{{ t('shippingCalculator.actualWeight', '实际重量') }}:</span>
                                  <span class="weight-value">{{ result.weight || 0 }}g</span>
                                </div>
                                <div class="weight-item">
                                  <span>{{ t('shippingCalculator.volumeWeight', '体积重') }}:</span>
                                  <span class="text-caption text-grey-7"
                                    >{{ `${t('shippingCalculator.common.l')} × ${t('shippingCalculator.common.w')} × ${t('shippingCalculator.common.h')}` }} / {{ result.volumeBase }}</span
                                  >
                                  <span class="weight-value">{{ result.volumeWeight || 0 }}g</span>
                                </div>
                                <div class="weight-item">
                                  <span>{{ t('shippingCalculator.chargeableWeight', '计费重量') }}:</span>
                                  <span class="weight-value text-weight-bold text-primary">{{ result.chargeableWeight || 0 }}g</span>
                                </div>
                              </div>
                            </q-item-section>
                          </q-item>
                        </q-list>
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="straighten" class="q-mr-xs" />
                          {{ t('shippingCalculator.packageLimit', '包裹限制') }}
                        </h6>
                        <q-list dense>
                          <q-item>
                            <q-item-section>
                              <div class="weight-info">
                                <div class="weight-item">
                                  <span>{{ t('shippingCalculator.weightLimit') }}:</span>
                                  <span class="weight-value">{{ result?.minWeight || 0 }} - {{ result?.maxWeight || 0 }}g</span>
                                </div>
                                <div class="weight-item" v-if="result?.sizeRestrictions">
                                  <span>{{ t('shippingCalculator.sizeLimit') }}:</span>
                                  <span class="weight-value">{{ formatSizeLimitM(result.sizeRestrictions) }}</span>
                                  <!-- <div class="size-limit-content" v-html="formatSizeLimitM(result.sizeRestrictions)"></div> -->
                                </div>
                              </div>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </div>
                      <!-- 费用详情 -->
                      <div class="col-12 col-md-6">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="credit_card" class="q-mr-xs" />
                          {{ t('shippingCalculator.feeBreakdown', '费用详情') }}
                        </h6>
                        <q-list dense>
                          <q-item>
                            <q-item-section>
                              <div class="fee-breakdown">
                                <div class="fee-item">
                                  <span>{{ t('shippingCalculator.freight', '基础运费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(getBaseFee(result)) }}</span>
                                </div>
                                <div class="fee-item">
                                  <span>{{ t('shippingCalculator.registrationFee', '操作费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(result.registrationFee) }}</span>
                                </div>
                                <!-- <div class="fee-item">
                                  <span>{{ t('shippingCalculator.operationFee', '操作费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(result.operationFee) }}</span>
                                </div>
                                <div class="fee-item">
                                  <span>{{ t('shippingCalculator.serviceFee', '服务费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(result.serviceFee) }}</span>
                                </div> -->
                                <div class="fee-item">
                                  <span>{{ t('shippingCalculator.customsFee', '清关费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(result.customsFee) }}</span>
                                </div>
                                <div class="fee-item">
                                  <span>{{ t('shippingCalculator.fuelFee', '燃油费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(result.fuelFee) }}</span>
                                </div>
                                <div class="fee-item">
                                  <span>{{ t('shippingCalculator.fuelFee', '附加费') }}:</span>
                                  <span class="fee-value">{{ fen2yuan(result.additionalFee) }}</span>
                                </div>
                                <q-separator class="q-my-sm" />
                                <div class="fee-item total-fee">
                                  <span class="text-weight-bold">{{ t('shippingCalculator.totalFee', '总费用') }}:</span>
                                  <span class="fee-value text-weight-bold text-primary">{{ fen2yuan(result.total) }}</span>
                                </div>
                              </div>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </div>
                    </div>

                    <!-- 产品特色和时效分布 - 并排显示 -->
                    <!-- <div class="row q-col-gutter-md q-mt-md">
                      <div v-if="result.timelinessInfo && result.timelinessInfo.timelinessInfos" class="col-12 col-md-6">
                        <h6 class="text-subtitle2 q-mb-sm">
                          <q-icon name="timeline" class="q-mr-xs" />
                          {{ t('shippingCalculator.timelinessInfo', '时效分布') }}
                        </h6>
                        <div class="timeliness-chart">
                          <div class="delivery-rate q-mb-sm">
                            <span class="text-body2">{{ t('shippingCalculator.deliveryRate', '妥投率') }}: </span>
                            <span class="text-weight-bold text-positive">{{ result.timelinessInfo.deliveryRate }}%</span>
                          </div>
                          <div class="timeliness-bars">
                            <div v-for="info in result.timelinessInfo.timelinessInfos" :key="info.timeInterval" class="timeliness-bar-item">
                              <div class="time-interval">{{ info.timeInterval }}天</div>
                              <div class="progress-container">
                                <q-linear-progress :value="parseFloat(info.rate) / 100" color="primary" size="8px" class="q-my-xs" />
                              </div>
                              <div class="rate-value">{{ info.rate }}%</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div> -->

                    <!-- 品类限制信息 -->
                    <div v-if="result.categoryRestrictions && result.categoryRestrictions.length > 0" class="q-mt-md">
                      <h6 class="text-subtitle2 q-mb-sm">
                        <q-icon name="category" class="q-mr-xs" />
                        {{ t('shippingCalculator.categoryRestrictions', '品类限制') }}
                      </h6>
                      <div class="category-restrictions-table">
                        <q-markup-table flat bordered dense>
                          <thead>
                            <tr>
                              <th class="text-left category-name-col">{{ t('shippingCalculator.categories') }}</th>
                              <th class="text-left allow-col">{{ t('shippingCalculator.allowedItems') }}</th>
                              <th class="text-left block-col">{{ t('shippingCalculator.blockedItems') }}</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr v-for="category in result.categoryRestrictions" :key="category.name">
                              <td class="category-name">{{ category.name }}</td>
                              <td class="allow-items">
                                <div v-if="getAllowList(category) && getAllowList(category).length > 0" class="restriction-tags">
                                  <!-- <q-chip v-for="item in getAllowList(category)" :key="item" size="sm" color="positive" text-color="white" :label="item" class="q-mr-xs q-mb-xs" /> -->
                                  <span size="xs" class="text-positive text-caption">
                                    {{ getAllowList(category).join(', ') }}
                                  </span>
                                </div>
                                <span v-else class="text-grey-6">-</span>
                              </td>
                              <td class="block-items">
                                <div v-if="getBlockList(category) && getBlockList(category).length > 0" class="restriction-tags">
                                  <!-- <q-chip v-for="item in getBlockList(category)" :key="item" size="sm" color="negative" text-color="white" :label="item" class="q-mr-xs q-mb-xs" /> -->
                                  <span size="xs" class="text-negative text-caption">
                                    {{ getBlockList(category).join(', ') }}
                                  </span>
                                </div>
                                <span v-else class="text-grey-6">-</span>
                              </td>
                            </tr>
                          </tbody>
                        </q-markup-table>
                      </div>
                    </div>

                    <!-- 收费标准 -->
                    <!-- <div v-if="result.pricingStandard" class="q-mt-md">
                      <h6 class="text-subtitle2 q-mb-sm">
                        <q-icon name="payments" class="q-mr-xs" />
                        {{ t('shippingCalculator.pricingStandard', '收费标准') }}
                      </h6>
                      <div class="pricing-standard">
                        <q-markup-table flat bordered>
                          <thead>
                            <tr>
                              <th class="text-left">{{ t('shippingCalculator.weightRange', '重量范围') }}</th>
                              <th class="text-left">{{ t('shippingCalculator.firstWeight', '首重价格') }}</th>
                              <th class="text-left">{{ t('shippingCalculator.additionalWeight', '续重价格') }}</th>
                              <th class="text-left">{{ t('shippingCalculator.maxWeight', '最大重量') }}</th>
                              <th class="text-left">{{ t('shippingCalculator.fuelSurcharge', '燃油费') }}</th>
                              <th class="text-left">{{ t('shippingCalculator.serviceFee', '服务费') }}</th>
                              <th class="text-left">{{ t('shippingCalculator.remoteAreaFee', '远程费') }}</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>{{ result.pricingStandard.weightRange || '-' }}</td>
                              <td>${{ result.pricingStandard.firstWeight || '0.00' }}/500g</td>
                              <td>${{ result.pricingStandard.additionalWeight || '0.00' }}/200g</td>
                              <td>${{ result.pricingStandard.maxWeight || '0.00' }}</td>
                              <td>${{ result.pricingStandard.fuelSurcharge || '0.00' }}</td>
                              <td>${{ result.pricingStandard.serviceFee || '0.00' }}</td>
                              <td>${{ result.pricingStandard.remoteAreaFee || '0.00' }}</td>
                            </tr>
                          </tbody>
                        </q-markup-table>
                      </div>
                    </div> -->

                    <!-- 操作按钮 -->
                    <div class="action-section q-mt-md q-pt-md">
                      <q-separator class="q-mb-md" />
                      <div class="row justify-end">
                        <q-btn flat color="grey-7" :label="t('shippingCalculator.common.collapse', '收起')" @click="toggleExpand(result.id)" />
                      </div>
                    </div>
                  </q-card-section>
                </div>
              </q-slide-transition>
            </q-card>
          </div>
        </div>

        <!-- 提示信息 -->
        <div v-if="results.length > 0" class="shipping-notice q-mt-md">
          <q-banner inline-actions class="text-white bg-info">
            <template #avatar>
              <q-icon name="info" />
            </template>
            {{ t('shippingCalculator.priceNotice') }}
          </q-banner>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

// 展开状态管理
const expandedItem = ref(null);

const { t } = useI18n();

const props = defineProps({
  results: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  // 新增：是否启用选择模式
  selectable: {
    type: Boolean,
    default: false,
  },
  // 新增：当前选中的方案
  selectedOption: {
    type: Object,
    default: null,
  },
  // 新增：显示模式：'full' 完整模式，'compact' 紧凑模式
  displayMode: {
    type: String,
    default: 'full',
    validator: (value) => ['full', 'compact'].includes(value),
  },
  // 新增：是否显示单选框
  showRadio: {
    type: Boolean,
    default: true,
  },
});

// 定义事件
const emit = defineEmits(['select-option', 'option-selected']);

// 状态管理 - 移除多个展开状态，改为单一展开状态
// const expandedItems = ref({});

//基础运费计算
const getBaseFee = (result) => {
  return result.total - result.registrationFee - result.operationFee - result.serviceFee - result.customsFee - result.additionalFee;
};

// 格式化尺寸限制信息
const formatSizeLimit = (sizeLimit) => {
  console.log('sizeLimit', sizeLimit);
  if (!sizeLimit) return '';

  try {
    const limit = typeof sizeLimit === 'string' ? JSON.parse(sizeLimit) : sizeLimit;
    const rules = [];

    // 基础尺寸限制
    if (limit.maxLength || limit.maxWidth || limit.maxHeight) {
      const dimensions = [];
      if (limit.maxLength) dimensions.push(`${t('shippingCalculator.sizeLimitOptions.length', '长')}≤${limit.maxLength}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (limit.maxWidth) dimensions.push(`${t('shippingCalculator.sizeLimitOptions.width', '宽')}≤${limit.maxWidth}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (limit.maxHeight) dimensions.push(`${t('shippingCalculator.sizeLimitOptions.height', '高')}≤${limit.maxHeight}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (dimensions.length > 0) {
        rules.push(`<div class="size-rule"><strong> ${dimensions.join(', ')}</div>`);
      }
    }

    // 最小尺寸限制
    if (limit.minLength || limit.minWidth || limit.minHeight) {
      const minDimensions = [];
      if (limit.minLength) minDimensions.push(`${t('shippingCalculator.sizeLimitOptions.length', '长')}≥${limit.minLength}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (limit.minWidth) minDimensions.push(`${t('shippingCalculator.sizeLimitOptions.width', '宽')}≥${limit.minWidth}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (limit.minHeight) minDimensions.push(`${t('shippingCalculator.sizeLimitOptions.height', '高')}≥${limit.minHeight}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (minDimensions.length > 0) {
        rules.push(`<div class="size-rule"><strong>${t('shippingCalculator.sizeLimitOptions.minDimension', '最小尺寸')}:</strong> ${minDimensions.join(', ')}</div>`);
      }
    }

    // 单边最大长度限制
    if (limit.maxSingleSide) {
      rules.push(
        `<div class="size-rule"><strong>${t('shippingCalculator.sizeLimitOptions.maxSingleSide', '单边最大')}:</strong> ≤${limit.maxSingleSide}${t(
          'shippingCalculator.sizeLimitOptions.cm',
          'cm'
        )}</div>`
      );
    }

    // 第二长边限制
    if (limit.maxSecondLongestSide) {
      rules.push(
        `<div class="size-rule"><strong>${t('shippingCalculator.sizeLimitOptions.maxSecondLongestSide', '第二长边')}:</strong> ≤${limit.maxSecondLongestSide}${t(
          'shippingCalculator.sizeLimitOptions.cm',
          'cm'
        )}</div>`
      );
    }

    // 三边和限制
    if (limit.maxTotalDimension) {
      rules.push(
        `<div class="size-rule"><strong>${t('shippingCalculator.sizeLimitOptions.maxTotalDimension', '三边和')}:</strong> ${t('shippingCalculator.sizeLimitOptions.length', '长')}+${t(
          'shippingCalculator.sizeLimitOptions.width',
          '宽'
        )}+${t('shippingCalculator.sizeLimitOptions.height', '高')}≤${limit.maxTotalDimension}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}</div>`
      );
    }

    // 长+2*(宽+高)限制
    if (limit.maxLengthPlusDoubleGirth) {
      rules.push(
        `<div class="size-rule"><strong>${t('shippingCalculator.sizeLimitOptions.maxLengthPlusDoubleGirth', '周长限制')}:</strong> ${t('shippingCalculator.sizeLimitOptions.length', '长')}+2×(${t(
          'shippingCalculator.sizeLimitOptions.width',
          '宽'
        )}+${t('shippingCalculator.sizeLimitOptions.height', '高')})≤${limit.maxLengthPlusDoubleGirth}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}</div>`
      );
    }

    // 超尺寸附加费
    if (limit.oversizeFee) {
      rules.push(
        `<div class="size-rule text-warning"><strong>${t('shippingCalculator.sizeLimitOptions.oversizeFee', '超尺寸费')}:</strong> ${t('shipping.common.yuan', '¥')}${(limit.oversizeFee / 100).toFixed(
          2
        )}${t('shippingCalculator.sizeLimitOptions.perTicket', '/票')}</div>`
      );
    }

    return rules.join('');
  } catch (error) {
    console.error('解析尺寸限制失败:', error);
    return '<div class="text-grey-6">尺寸限制信息格式错误</div>';
  }
};

const formatSizeLimitM = (sizeLimit) => {
  console.log('sizeLimit', sizeLimit);
  if (!sizeLimit) return '';

  try {
    const limit = typeof sizeLimit === 'string' ? JSON.parse(sizeLimit) : sizeLimit;
    const rules = [];

    // 基础尺寸限制
    if (limit.maxLength || limit.maxWidth || limit.maxHeight) {
      const dimensions = [];
      if (limit.maxLength) dimensions.push(`${t('shippingCalculator.sizeLimitOptions.length', '长')}≤${limit.maxLength}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (limit.maxWidth) dimensions.push(`${t('shippingCalculator.sizeLimitOptions.width', '宽')}≤${limit.maxWidth}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (limit.maxHeight) dimensions.push(`${t('shippingCalculator.sizeLimitOptions.height', '高')}≤${limit.maxHeight}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (dimensions.length > 0) {
        rules.push(`${dimensions.join(', ')}`);
        rules.push(`, `);
      }
    }

    // 最小尺寸限制
    if (limit.minLength || limit.minWidth || limit.minHeight) {
      const minDimensions = [];
      if (limit.minLength) minDimensions.push(`${t('shippingCalculator.sizeLimitOptions.length', '长')}≥${limit.minLength}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (limit.minWidth) minDimensions.push(`${t('shippingCalculator.sizeLimitOptions.width', '宽')}≥${limit.minWidth}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (limit.minHeight) minDimensions.push(`${t('shippingCalculator.sizeLimitOptions.height', '高')}≥${limit.minHeight}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
      if (minDimensions.length > 0) {
        rules.push(`${t('shippingCalculator.sizeLimitOptions.minDimension', '最小尺寸')}: ${minDimensions.join(', ')}`);
      }
    }

    // 单边最大长度限制
    if (limit.maxSingleSide) {
      rules.push(`${t('shippingCalculator.sizeLimitOptions.maxSingleSide', '单边最大')}: ≤${limit.maxSingleSide}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
    }

    // 第二长边限制
    if (limit.maxSecondLongestSide) {
      rules.push(`${t('shippingCalculator.sizeLimitOptions.maxSecondLongestSide', '第二长边')}: ≤${limit.maxSecondLongestSide}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`);
    }

    // 三边和限制
    if (limit.maxTotalDimension) {
      rules.push(
        `${t('shippingCalculator.sizeLimitOptions.maxTotalDimension', '三边和')}: ${t('shippingCalculator.sizeLimitOptions.length', '长')}+${t('shippingCalculator.sizeLimitOptions.width', '宽')}+${t(
          'shippingCalculator.sizeLimitOptions.height',
          '高'
        )}≤${limit.maxTotalDimension}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`
      );
    }

    // 长+2*(宽+高)限制
    if (limit.maxLengthPlusDoubleGirth) {
      rules.push(
        `${t('shippingCalculator.sizeLimitOptions.maxLengthPlusDoubleGirth', '周长限制')}:</strong> ${t('shippingCalculator.sizeLimitOptions.length', '长')}+2×(${t(
          'shippingCalculator.sizeLimitOptions.width',
          '宽'
        )}+${t('shippingCalculator.sizeLimitOptions.height', '高')})≤${limit.maxLengthPlusDoubleGirth}${t('shippingCalculator.sizeLimitOptions.cm', 'cm')}`
      );
    }

    // 超尺寸附加费
    if (limit.oversizeFee) {
      rules.push(
        `${t('shippingCalculator.sizeLimitOptions.oversizeFee', '超尺寸费')}:${t('shipping.common.yuan', '¥')}${(limit.oversizeFee / 100).toFixed(2)}${t(
          'shippingCalculator.sizeLimitOptions.perTicket',
          '/票'
        )}`
      );
    }

    return rules.join('');
  } catch (error) {
    console.error('解析尺寸限制失败:', error);
    return '';
  }
};

// 切换展开/收起状态 - 一次只能展开一个
const toggleExpand = (resultId) => {
  if (expandedItem.value === resultId) {
    expandedItem.value = null; // 收起当前展开的项
  } else {
    expandedItem.value = resultId; // 展开新项，自动收起其他项
  }
};

// 检查是否选中
const isSelected = (result) => {
  if (!props.selectable || !props.selectedOption || !result) return false;

  // 根据数据结构判断，优先使用 productId 和 priceId
  if (result.productId && result.priceId) {
    return props.selectedOption.productId === result.productId && props.selectedOption.priceId === result.priceId;
  }

  // 兜底使用 id 判断
  return props.selectedOption.id === result.id;
};

// 选择运费方案
const selectOption = (result) => {
  if (!props.selectable) return;

  emit('select-option', result);
  emit('option-selected', result);
};

// 获取允许列表（兼容不同的字段名）
const getAllowList = (category) => {
  return category.allowList || category.allowlist || [];
};

// 获取禁止列表（兼容不同的字段名）
const getBlockList = (category) => {
  return category.blockList || category.blocklist || [];
};
</script>

<style lang="scss" scoped>
.shipping-results {
  .results-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    // 紧凑模式样式
    &.compact-mode {
      box-shadow: none;
      border: none;

      .q-card-section {
        padding: 8px 0;
      }
    }
  }

  .shipping-result-item {
    position: relative;

    .shipping-option-card {
      border-radius: 12px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      // 选中状态样式
      &.selected-card {
        border-color: #1976d2;
        border-width: 2px;
        box-shadow: 0 4px 20px rgba(25, 118, 210, 0.3);
        background-color: #f3f8ff;
        border-radius: 12px;
        position: relative;

        // 添加选中标识
        &::after {
          content: '✓';
          position: absolute;
          top: 8px;
          right: 8px;
          width: 24px;
          height: 24px;
          background: #1976d2;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: bold;
          z-index: 5;
        }
      }
    }

    // 选中状态样式
    &.selected {
      .shipping-option-header {
        background-color: #f3f8ff;
        border-radius: 12px;
      }
    }

    // 头像容器
    .avatar-container {
      position: relative;

      .avatar-selection-indicator {
        position: absolute;
        top: -4px;
        right: -4px;
        z-index: 2;
        background: white;
        border-radius: 50%;
        padding: 2px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .shipping-option-header {
    padding: 16px;

    &:hover {
      background-color: #f8f9fa;
    }

    &.clickable {
      cursor: pointer;

      &:hover {
        background-color: #e3f2fd;
        border-radius: 12px;
      }
    }
  }

  &.unavailable {
    .shipping-option-card {
      background-color: #f5f5f5;
      border: 1px solid #e0e0e0;
      opacity: 0.7;

      &:hover {
        box-shadow: none;
      }
    }

    .shipping-option-header {
      &:hover {
        background-color: #f5f5f5;
      }

      .q-avatar {
        background-color: #bdbdbd !important;
      }

      .q-item-label {
        color: #999;
      }

      .shipping-basic-info {
        .shipping-info-item {
          color: #999;
        }
      }
    }
  }
}

.shipping-basic-info {
  .shipping-info-item {
    display: flex;
    align-items: center;
    color: #666;
  }
}

.price-display {
  .currency-symbol {
    font-size: 0.9rem;
    color: #666;
  }

  .price-amount {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1976d2;
  }
}

.unavailable-reason {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.shipping-details-expanded {
  background-color: #fafafa;

  .fee-breakdown {
    .fee-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;

      &.total-fee {
        padding-top: 8px;
        font-size: 1.1rem;
      }
    }

    .fee-value {
      font-weight: 500;
      color: #1976d2;
    }
  }

  .weight-info {
    .weight-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
    }

    .weight-value {
      font-weight: 500;
      color: #1976d2;
    }
  }

  .features-content {
    background-color: #fff;
    padding: 12px;
    border-radius: 8px;
    // border-left: 4px solid #1976d2;
  }

  .timeliness-chart {
    .delivery-rate {
      padding: 8px 12px;
      background-color: #e8f5e8;
      border-radius: 6px;
    }

    .timeliness-bars {
      .timeliness-bar-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 6px 0;

        .time-interval {
          min-width: 60px;
          font-size: 0.875rem;
          color: #666;
        }

        .progress-container {
          flex: 1;
        }

        .rate-value {
          min-width: 40px;
          text-align: right;
          font-weight: 500;
          color: #1976d2;
        }
      }
    }
  }

  .category-restrictions-table {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;

    .q-markup-table {
      th {
        background-color: #f5f5f5;
        font-weight: 600;
        color: #333;
        padding: 8px 6px;
        font-size: 0.75rem;

        &.category-name-col {
          width: 15%;
          min-width: 80px;
        }

        &.allow-col,
        &.block-col {
          width: 42.5%;
        }
      }

      td {
        padding: 6px;
        font-size: 0.75rem;
        border-bottom: 1px solid #e0e0e0;
        vertical-align: top;

        &.category-name {
          font-weight: 500;
          background-color: #fafafa;
          white-space: nowrap;
        }

        &.allow-items,
        &.block-items {
          max-width: 200px; /* 控制列宽，根据需要调整 */
          word-break: break-word; /* 允许在单词/字符间断行（适合中文） */
          overflow-wrap: break-word; /* 更现代的写法，优先使用 */
          white-space: normal; /* 禁止强制不换行 */

          .restriction-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 2px;

            .q-chip {
              font-size: 0.65rem;
              height: 18px;
              padding: 0 6px;
              margin: 1px;
            }
          }
        }
      }

      tr:last-child td {
        border-bottom: none;
      }
    }
  }

  .pricing-standard {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;

    .q-markup-table {
      th {
        background-color: #f5f5f5;
        font-weight: 600;
        color: #333;
        padding: 12px 8px;
        font-size: 0.875rem;
      }

      td {
        padding: 10px 8px;
        font-size: 0.875rem;
        border-bottom: 1px solid #e0e0e0;

        &:first-child {
          font-weight: 500;
        }
      }

      tr:last-child td {
        border-bottom: none;
      }
    }
  }

  .action-section {
    background-color: #fff;
    margin: 0 -16px -16px -16px;
    padding: 16px;
    border-radius: 0 0 12px 12px;
  }
}

.shipping-notice {
  .q-banner {
    border-radius: 8px;
  }
}

// 移动端紧凑卡片样式
.mobile-compact-card {
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative; // 为推荐标志定位

  &.selected-card {
    border-color: var(--q-primary);
    border-width: 2px;
    background-color: rgba(25, 118, 210, 0.08);
    box-shadow: 0 2px 12px rgba(25, 118, 210, 0.2);

    // 添加选中标识
    &::after {
      content: '✓';
      position: absolute;
      top: 6px;
      right: 6px;
      width: 20px;
      height: 20px;
      background: var(--q-primary);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      z-index: 5;
    }
  }

  .compact-card-content {
    padding: 12px;
  }

  .shipping-name {
    font-size: 0.9rem;
    line-height: 1.2;
    margin-bottom: 2px;
  }

  .shipping-meta {
    font-size: 0.75rem;
    line-height: 1.1;

    .q-badge {
      font-size: 0.6rem;
      padding: 1px 4px;
    }
  }

  .shipping-price {
    font-size: 1rem;
    margin-bottom: 2px;
  }
}

// 推荐标志样式 - 右上角角标
.recommended-badge {
  position: absolute;
  top: -15px;
  left: -1px;
  z-index: 10;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-radius: 12px;
  padding: 2px 8px;
  box-shadow: 0 2px 8px rgba(238, 90, 36, 0.3);
  border: 2px solid white;

  .recommended-text {
    color: white;
    font-size: 0.7rem;
    font-weight: bold;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
}

// 移动端详情展开区域
.mobile-details {
  .detail-section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    h6 {
      font-size: 0.85rem;
      margin-bottom: 8px;
      color: var(--q-primary);
    }
  }

  .fee-breakdown {
    .fee-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      font-size: 0.8rem;

      &.total-fee {
        border-top: 1px solid #e0e0e0;
        margin-top: 8px;
        padding-top: 8px;
        font-size: 0.85rem;
      }

      .fee-value {
        color: var(--q-primary);
      }
    }
  }

  .weight-info {
    .weight-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      font-size: 0.8rem;

      .weight-value {
        color: var(--q-primary);
      }
    }
  }

  .size-limit-content {
    .size-rule {
      margin-bottom: 8px;
      padding: 6px 8px;
      background-color: #f8f9fa;
      border-radius: 4px;
      font-size: 0.8rem;
      line-height: 1.4;

      strong {
        color: var(--q-primary);
      }

      &.text-warning {
        background-color: #fff3cd;
        border-left: 3px solid #ffc107;
      }
    }
  }

  .category-restrictions-mobile {
    .restriction-item {
      background-color: #f8f9fa;
      border-radius: 6px;
      padding: 8px;

      .restriction-category {
        font-size: 0.85rem;
        color: var(--q-primary);
      }

      .restriction-section {
        .restriction-label {
          font-size: 0.75rem;
          // font-weight: 500;
        }

        .restriction-tags {
          margin-top: 4px;

          .q-chip {
            font-size: 0.65rem;
            height: 20px;
            margin: 2px;
          }
        }
      }
    }
  }

  .features-content {
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    line-height: 1.4;
    color: #666;
  }
}

/* 移动端适配 */
@media (max-width: 600px) {
  .shipping-results {
    .shipping-result-item {
      margin-bottom: 8px;

      .shipping-option-header {
        padding: 12px;

        .q-item-section {
          &.side {
            min-width: 40px;
          }

          &.avatar {
            min-width: 60px;
          }
        }
      }

      .q-avatar {
        width: 40px !important;
        height: 40px !important;
        font-size: 0.9rem;
      }

      .price-display {
        .price-amount {
          font-size: 1.1rem;
        }
      }

      .shipping-basic-info {
        .row {
          flex-direction: column;
          gap: 4px;
        }

        .shipping-info-item {
          font-size: 0.75rem;
        }
      }
    }
  }
}
</style>
