<template>
  <div>
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="person" size="xs" color="primary" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('profile.title') }}</span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="q-pa-md">
      <!-- 个人信息卡片 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="row items-center q-mb-sm">
            <div class="text-subtitle1 text-weight-medium">
              <q-icon name="badge" color="primary" size="xs" class="q-mr-xs" />
              {{ $t('profile.sections.basicInfo') }}
            </div>
            <q-space />
            <q-btn v-if="!editingBasic" color="primary" flat icon="edit" :label="$t('profile.buttons.edit')" @click="startEditBasic" dense />
          </div>

          <!-- 查看模式 -->
          <div v-if="!editingBasic" class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.username') }}</div>
                <div class="col-8">{{ userInfo.name || $t('profile.values.notSet') }}</div>
              </div>
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.nickname') }}</div>
                <div class="col-8">{{ userInfo.nickname || $t('profile.values.notSet') }}</div>
              </div>
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.gender') }}</div>
                <div class="col-8">{{ getSexText(userInfo.sex) }}</div>
              </div>
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.birthday') }}</div>
                <div class="col-8">{{ userInfo.birthday ? new Date(userInfo.birthday).toLocaleDateString() : $t('profile.values.notSet') }}</div>
              </div>
            </div>
            <div class="col-12 col-md-6">
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.avatar') }}</div>
                <div class="col-8">
                  <q-avatar size="60px">
                    <img v-if="userInfo.avatar" :src="userInfo.avatar" :alt="$t('profile.values.userAvatar')" />
                    <q-icon v-else name="person" size="40px" color="grey-5" />
                  </q-avatar>
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.subscribed') }}</div>
                <div class="col-8">
                  <q-badge :color="userInfo.subscribed ? 'positive' : 'grey'" :label="userInfo.subscribed ? $t('profile.values.subscribed') : $t('profile.values.unsubscribed')" />
                </div>
              </div>
            </div>
          </div>

          <!-- 编辑模式 -->
          <q-form v-else ref="basicFormRef" @submit="saveBasicInfo" class="edit-form">
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-6">
                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.username') }}</div>
                  <q-input
                    v-model="basicForm.name"
                    :error="$vBasic.name.$error"
                    :error-message="$vBasic.name.$errors[0]?.$message"
                    outlined
                    dense
                    class="form-control"
                    :placeholder="$t('profile.placeholders.username')"
                    @blur="$vBasic.name.$touch" />
                </div>

                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.nickname') }}</div>
                  <q-input
                    v-model="basicForm.nickname"
                    :error="$vBasic.nickname.$error"
                    :error-message="$vBasic.nickname.$errors[0]?.$message"
                    outlined
                    dense
                    class="form-control"
                    :placeholder="$t('profile.placeholders.nickname')"
                    @blur="$vBasic.nickname.$touch" />
                </div>

                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.gender') }}</div>
                  <q-select
                    v-model="basicForm.sex"
                    :options="sexOptions"
                    :error="$vBasic.sex.$error"
                    :error-message="$vBasic.sex.$errors[0]?.$message"
                    outlined
                    dense
                    emit-value
                    map-options
                    class="form-control"
                    @blur="$vBasic.sex.$touch" />
                </div>

                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.birthday') }}</div>
                  <q-input
                    v-model="basicForm.birthday"
                    :error="$vBasic.birthday.$error"
                    :error-message="$vBasic.birthday.$errors[0]?.$message"
                    outlined
                    dense
                    class="form-control"
                    :placeholder="$t('profile.placeholders.birthday')"
                    @blur="$vBasic.birthday.$touch"
                    readonly>
                    <template v-slot:append>
                      <q-icon name="event" class="cursor-pointer">
                        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                          <q-date v-model="basicForm.birthday" mask="YYYY-MM-DD">
                            <div class="row items-center justify-end">
                              <q-btn v-close-popup label="确定" color="primary" flat />
                            </div>
                          </q-date>
                        </q-popup-proxy>
                      </q-icon>
                    </template>
                  </q-input>
                </div>
              </div>

              <div class="col-12 col-md-6">
                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.avatar') }}</div>
                  <SingleImageUpload v-model="basicForm.avatar" width="120px" height="120px" :max-file-size="2 * 1024 * 1024" class="avatar-upload-component" />
                  <div class="text-caption text-grey-7 q-mt-sm">{{ $t('profile.tips.avatarFormat') }}</div>
                </div>

                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.subscribed') }}</div>
                  <q-toggle v-model="basicForm.subscribed" :label="basicForm.subscribed ? $t('profile.values.subscribed') : $t('profile.values.unsubscribed')" color="primary" />
                  <div class="text-caption text-grey-7 q-mt-xs">接收产品更新和优惠信息</div>
                </div>
              </div>
            </div>

            <div class="form-actions">
              <q-btn flat :label="$t('profile.buttons.cancel')" color="grey-7" class="q-mr-sm" @click="cancelEditBasic" />
              <q-btn type="submit" :label="$t('profile.buttons.save')" color="primary" :loading="savingBasic" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>

      <!-- 联系方式卡片 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="row items-center q-mb-sm">
            <div class="text-subtitle1 text-weight-medium">
              <q-icon name="contact_phone" color="primary" size="xs" class="q-mr-xs" />
              {{ $t('profile.sections.contactInfo') }}
            </div>
            <q-space />
            <q-btn v-if="!editingContact" color="primary" flat icon="edit" :label="$t('profile.buttons.edit')" @click="startEditContact" dense />
          </div>

          <!-- 查看模式 -->
          <div v-if="!editingContact" class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.email') }}</div>
                <div class="col-8">
                  {{ userInfo.email }}
                  <q-badge v-if="userInfo.status === 0" color="positive" class="q-ml-sm">{{ $t('profile.values.verified') }}</q-badge>
                  <q-badge v-if="userInfo.status === 2" color="negative" class="q-ml-sm">{{ $t('profile.values.unverified') }}</q-badge>
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-4 text-grey-7">{{ $t('profile.fields.mobile') }}</div>
                <div class="col-8">{{ formatMobileDisplay(userInfo.mobile) }}</div>
              </div>
            </div>
          </div>

          <!-- 编辑模式 -->
          <q-form v-else ref="contactFormRef" @submit="saveContactInfo" class="edit-form">
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-8">
                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.email') }}</div>
                  <div class="email-display">
                    {{ userInfo.email }}
                    <q-badge v-if="userInfo.status === 0" color="positive" class="q-ml-sm">{{ $t('profile.values.verified') }}</q-badge>
                    <q-badge v-if="userInfo.status === 2" color="negative" class="q-ml-sm">{{ $t('profile.values.unverified') }}</q-badge>
                    <!-- <div v-if="userInfo.status !== 1" class="q-mt-xs">
                      <q-btn flat dense size="sm" color="primary" :label="$t('profile.buttons.sendVerifyEmail')" @click="sendVerifyEmail" />
                    </div> -->
                  </div>
                </div>

                <div class="form-group">
                  <div class="form-label">{{ $t('profile.fields.mobile') }}</div>
                  <div class="mobile-edit">
                    <div class="row q-col-gutter-sm">
                      <div class="col-4 col-sm-3">
                        <q-select v-model="contactForm.phoneCode" :options="phoneCodeOptions" outlined dense map-options emit-value class="form-control" />
                      </div>
                      <div class="col-8 col-sm-9">
                        <q-input v-model="contactForm.mobile" :placeholder="$t('profile.placeholders.mobile')" outlined dense class="form-control" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-actions">
              <q-btn flat :label="$t('profile.buttons.cancel')" color="grey-7" class="q-mr-sm" @click="cancelEditContact" />
              <q-btn type="submit" :label="$t('profile.buttons.save')" color="primary" :loading="savingContact" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useUserStore } from '~/store/user';
import UserApi from '~/composables/userApi';
import AuthApi from '~/composables/authApi';
import { useI18n } from 'vue-i18n';
import useVuelidate from '@vuelidate/core';
import { createValidators } from '~/utils/i18n-validators';
import SingleImageUpload from '~/components/SingleImageUpload.vue';

const $q = useQuasar();
const userStore = useUserStore();
const { t } = useI18n();

// 设置验证器
const validators = createValidators(t);

// 用户信息
const userInfo = ref({
  name: '',
  nickname: '',
  sex: 0, // 后端字段
  mobile: '',
  email: '',
  avatar: '',
  birthday: null, // 生日
  subscribed: true, // 是否订阅邮件通知
  status: 0, // 邮箱验证状态
});

// 性别选项
const sexOptions = [
  { label: t('profile.gender.secret'), value: 0 },
  { label: t('profile.gender.male'), value: 1 },
  { label: t('profile.gender.female'), value: 2 },
];

// 基本信息编辑状态
const editingBasic = ref(false);
const basicFormRef = ref(null);
const savingBasic = ref(false);
const basicForm = reactive({
  name: '',
  nickname: '',
  sex: 0, // 后端字段
  avatar: '',
  birthday: null,
  subscribed: true,
});

// 头像上传功能已集成到 SingleImageUpload 组件中

// 联系方式编辑状态
const editingContact = ref(false);
const contactFormRef = ref(null);
const savingContact = ref(false);
const contactForm = reactive({
  phoneCode: '+1',
  mobile: '',
});

// 表单验证规则
const basicFormRules = {
  name: { required: validators.required, minLength: validators.minLength(2), maxLength: validators.maxLength(20) },
  nickname: { maxLength: validators.maxLength(30) },
  sex: { required: validators.required },
  birthday: { required: validators.required },
  subscribed: {},
};

const contactFormRules = {
  mobile: { maxLength: validators.maxLength(20) },
};

// 设置验证
const $vBasic = useVuelidate(basicFormRules, basicForm);
const $vContact = useVuelidate(contactFormRules, contactForm);

// 页面加载时获取用户信息
onMounted(async () => {
  await getUserInfo();
});

// 获取用户信息
async function getUserInfo() {
  try {
    await userStore.getUserInfo();
    userInfo.value = userStore.userInfo;
  } catch (error) {
    console.error('获取用户信息失败', error);
    $q.notify({
      color: 'negative',
      message: t('profile.notifications.getUserInfoFailed'),
      icon: 'error',
    });
  }
}

// 获取性别文本
function getSexText(sex) {
  const option = sexOptions.find((opt) => opt.value === sex);
  return option ? option.label : t('profile.gender.secret');
}

// 格式化手机号显示
function formatMobileDisplay(mobile) {
  if (!mobile) return t('profile.values.notSet');

  // 如果手机号已经包含区号，直接显示
  if (mobile.startsWith('+')) {
    return mobile;
  }

  // 否则默认添加+1区号
  return `+1 ${mobile}`;
}

// 开始编辑基本信息
function startEditBasic() {
  basicForm.name = userInfo.value.name || '';
  basicForm.nickname = userInfo.value.nickname || '';
  basicForm.sex = userInfo.value.sex || 0;
  basicForm.avatar = userInfo.value.avatar || '';
  basicForm.birthday = userInfo.value.birthday || null;
  basicForm.subscribed = userInfo.value.subscribed !== undefined ? userInfo.value.subscribed : true;
  editingBasic.value = true;
}

// 取消编辑基本信息
function cancelEditBasic() {
  editingBasic.value = false;
}

// 头像预览功能已集成到 SingleImageUpload 组件中

// 保存基本信息
async function saveBasicInfo() {
  try {
    // 表单验证
    const isValid = await $vBasic.value.$validate();
    if (!isValid) {
      $q.notify({
        color: 'negative',
        message: '请检查表单填写是否正确',
        icon: 'error',
      });
      return;
    }

    savingBasic.value = true;

    // 构建请求数据（根据后端 AppMemberUserUpdateReqVO 要求）
    const requestData = {
      name: basicForm.name,
      nickname: basicForm.nickname,
      sex: basicForm.sex, // 后端字段是 sex
      avatar: basicForm.avatar || userInfo.value.avatar || '', // 头像URL
      mobile: userInfo.value.mobile || '', // 保持现有手机号
      birthday: basicForm.birthday, // 生日
      subscribed: basicForm.subscribed, // 是否订阅
    };

    // 调用API更新用户信息
    const { code, msg } = await UserApi.updateUser(requestData);

    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: t('profile.notifications.basicInfoUpdated'),
        icon: 'check',
      });

      // 重新获取用户信息
      await getUserInfo();
      editingBasic.value = false;
    } else {
      $q.notify({
        color: 'negative',
        message: msg || t('profile.notifications.updateFailed'),
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('保存基本信息失败', error);
    $q.notify({
      color: 'negative',
      message: t('profile.notifications.saveFailed'),
      icon: 'error',
    });
  } finally {
    savingBasic.value = false;
  }
}

// 开始编辑联系方式
function startEditContact() {
  // 解析现有手机号
  const mobile = userInfo.value.mobile || '';
  if (mobile.startsWith('+')) {
    const parts = mobile.split(' ');
    contactForm.phoneCode = parts[0] || '+1';
    contactForm.mobile = parts.slice(1).join(' ') || '';
  } else {
    contactForm.phoneCode = '+1';
    contactForm.mobile = mobile;
  }
  editingContact.value = true;
}

// 取消编辑联系方式
function cancelEditContact() {
  editingContact.value = false;
}

// 手机验证码功能已移除，简化手机号编辑流程

// 发送邮箱验证邮件
async function sendVerifyEmail() {
  try {
    const { code, msg } = await AuthApi.resend({
      email: userInfo.value.email,
    });

    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: '验证邮件已发送，请查收',
        icon: 'check',
      });
    } else {
      $q.notify({
        color: 'negative',
        message: msg || '发送验证邮件失败',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('发送验证邮件失败', error);
    $q.notify({
      color: 'negative',
      message: '发送验证邮件失败，请重试',
      icon: 'error',
    });
  }
}

// 保存联系方式
async function saveContactInfo() {
  try {
    savingContact.value = true;

    // 格式化手机号：区号 + 空格 + 手机号
    const formattedMobile = contactForm.mobile ? `${contactForm.phoneCode} ${contactForm.mobile}` : '';

    // 构建请求数据，更新用户信息
    const requestData = {
      name: userInfo.value.name,
      nickname: userInfo.value.nickname,
      sex: userInfo.value.sex || 0,
      avatar: userInfo.value.avatar || '',
      mobile: formattedMobile,
      birthday: userInfo.value.birthday,
      subscribed: userInfo.value.subscribed !== undefined ? userInfo.value.subscribed : true,
    };

    // 调用API更新用户信息
    const { code, msg } = await UserApi.updateUser(requestData);

    if (code === 0) {
      $q.notify({
        color: 'positive',
        message: '联系方式更新成功',
        icon: 'check',
      });

      // 重新获取用户信息
      await getUserInfo();
      editingContact.value = false;
    } else {
      $q.notify({
        color: 'negative',
        message: msg || '更新失败，请重试',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('保存联系方式失败', error);
    $q.notify({
      color: 'negative',
      message: '保存失败，请重试',
      icon: 'error',
    });
  } finally {
    savingContact.value = false;
  }
}
</script>

<style lang="scss" scoped>
.q-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);

  .q-card-section {
    padding: 20px;
  }

  .text-subtitle1 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
  }
}

// 表单样式
.edit-form {
  margin-top: 16px;

  .form-group {
    margin-bottom: 20px;
  }

  .form-label {
    font-size: 0.9rem;
    color: #34495e;
    margin-bottom: 6px;
    font-weight: 500;
  }

  .form-control {
    width: 100%;

    .q-field__control {
      height: 42px;
      border-radius: 8px;
    }

    .q-field__control-container {
      padding: 0 12px;
    }
  }

  .avatar-upload-component {
    margin-bottom: 8px;
  }

  .email-display,
  .mobile-display {
    padding: 8px 0;
    font-size: 0.95rem;
  }

  .mobile-edit {
    .verification-btn {
      height: 42px;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    gap: 12px;

    .q-btn {
      min-width: 90px;
      height: 40px;
      border-radius: 8px;
      font-weight: 500;
    }
  }
}

// 查看模式样式优化
.row.q-mb-md {
  margin-bottom: 12px;

  .col-4 {
    font-size: 0.9rem;
    font-weight: 500;
  }

  .col-8 {
    font-size: 0.95rem;
    color: #2c3e50;
  }
}

// 移动端优化
@media (max-width: 599px) {
  .q-card-section {
    padding: 16px;
  }

  .row.q-mb-md {
    margin-bottom: 10px;
  }

  .text-h6 {
    font-size: 1.1rem;
  }

  .text-subtitle1 {
    font-size: 1rem;
  }

  // 在移动端视图下调整布局
  .col-4,
  .col-8 {
    padding-top: 6px;
    padding-bottom: 6px;
  }

  .edit-form {
    .form-group {
      margin-bottom: 16px;
    }

    .form-label {
      font-size: 0.85rem;
      margin-bottom: 4px;
    }

    .form-control {
      .q-field__control {
        height: 38px;
      }
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 12px;

      .q-btn {
        min-width: 80px;
        height: 36px;
      }
    }
  }
}
</style>
