/**
 * 运费计算相关工具函数
 */

/**
 * 转换运费计算API响应数据为统一的前端格式
 * 适用于 shipping-calculator.vue 中的运费查询结果
 * @param {Array} apiResults - API返回的运费计算结果数组
 * @param {Function} getCategoryNameById - 获取分类名称的函数
 * @returns {Array} 转换后的统一格式数据
 */
export function transformShippingQuoteResults(apiResults, getCategoryNameById = null) {
  if (!Array.isArray(apiResults) || apiResults.length === 0) {
    return [];
  }

  return apiResults.map((item) => {
    const result = {
      // 基础标识信息
      id: item.id,
      priceId: item.priceId,
      productId: item.productId, // 添加 productId 以支持包裹创建场景
      zoneCode: item.zoneCode,

      // 基础显示信息
      name: item.name,
      iconUrl: item.iconUrl,
      features: item.features,
      transitTime: item.transitTime || '-',

      // 服务特性
      taxInclude: item.taxInclude || false,
      available: item.available !== false,
      unavailableReason: item.unavailableReason,
      sort: item.sort,
      iossEnabled: item.iossEnabled || false,
      freeInsure: item.freeInsure || false,
      deliveryRate: item.deliveryRate || 0.98,
      discountRate: item.discountRate || 0,
      tariffRate: item.tariffRate || 0,
      prepayTariff: item.prepayTariff || false,
      serviceLevel: item.serviceLevel || 'standard',

      // 商品类型支持
      electronic: item.electronic || false,
      cosmetic: item.cosmetic || false,
      clothing: item.clothing || false,
      liquid: item.liquid || false,
      large: item.large || false,
      recommended: item.recommended || false,

      // 重量和尺寸信息
      weight: item.weight || 0,
      length: item.length || 0,
      width: item.width || 0,
      height: item.height || 0,
      volumeWeight: item.volumeWeight || 0,
      chargeableWeight: item.chargeableWeight || 0,

      // 费用信息
      total: item.total || 0,
      freight: item.freight || 0,
      customsFee: item.customsFee || 0,
      fuelFee: item.fuelFee || 0,
      registrationFee: item.registrationFee || 0,
      operationFee: item.operationFee || 0,
      serviceFee: item.serviceFee || 0,
      feeFirst: item.feeFirst || 0,
      feeContinue: item.feeContinue || 0,
      additionalFee: item.additionalFee || 0,

      // 重量计费规则
      weightFirst: item.weightFirst || 0,
      weightContinue: item.weightContinue || 0,
      needVolumeCal: item.needVolumeCal !== false,
      volumeBase: item.volumeBase || 8000,
      minWeight: item.minWeight || 0,
      maxWeight: item.maxWeight || 0,

      // 限制信息
      sizeRestrictions: item.sizeRestrictions || '',
      dimensionRestriction: item.dimensionRestriction || '-',
      volumeWeightRule: item.volumeWeightRule || '-',
      categoryRestrictions: item.categoryRestrictions || '',
    };

    // 处理时效信息
    if (item.logisticsTimeliness) {
      result.timelinessInfo = item.logisticsTimeliness;
    }

    // 处理品类限制信息
    if (item.categoryRestrictions) {
      try {
        const categoryRestrictions = typeof item.categoryRestrictions === 'string' ? JSON.parse(item.categoryRestrictions) : item.categoryRestrictions;

        result.categoryRestrictions = categoryRestrictions.map((restriction) => ({
          name: getCategoryNameById ? getCategoryNameById(restriction.id) : `分类${restriction.id}`,
          allowList: restriction.allowList ? restriction.allowList.map((id) => (getCategoryNameById ? getCategoryNameById(id) : `分类${id}`)) : [],
          blockList: restriction.blockList ? restriction.blockList.map((id) => (getCategoryNameById ? getCategoryNameById(id) : `分类${id}`)) : [],
        }));
      } catch (error) {
        console.error('解析品类限制数据失败:', error);
        result.categoryRestrictions = [];
      }
    }

    // 处理收费标准
    if (item.pricingStandard) {
      result.pricingStandard = item.pricingStandard;
    }

    return result;
  });
}

/**
 * 检查两个运费方案是否相同
 * @param {Object} plan1 - 方案1
 * @param {Object} plan2 - 方案2
 * @returns {boolean} 是否相同
 */
export function isSameShippingPlan(plan1, plan2) {
  if (!plan1 || !plan2) return false;

  // 优先使用 productId 和 priceId 判断
  if (plan1.productId && plan1.priceId && plan2.productId && plan2.priceId) {
    return plan1.productId === plan2.productId && plan1.priceId === plan2.priceId;
  }

  // 兜底使用 id 判断
  return plan1.id === plan2.id;
}

/**
 * 按价格排序运费方案
 * @param {Array} plans - 运费方案数组
 * @param {string} order - 排序方式：'asc' 升序，'desc' 降序
 * @returns {Array} 排序后的数组
 */
export function sortShippingPlansByPrice(plans, order = 'asc') {
  if (!Array.isArray(plans)) return [];

  return [...plans].sort((a, b) => {
    const priceA = parseFloat(a.total || a.totalFee || 0);
    const priceB = parseFloat(b.total || b.totalFee || 0);

    return order === 'asc' ? priceA - priceB : priceB - priceA;
  });
}

/**
 * 格式化妥投率显示
 * @param {number|string} rate - 妥投率
 * @returns {string} 格式化后的妥投率字符串
 */
export function formatDeliveryRate(rate) {
  if (!rate) return '未知';

  // 如果已经是百分比格式（包含%），直接返回
  if (typeof rate === 'string' && rate.includes('%')) {
    return rate;
  }

  // 如果是数字，转换为百分比
  const numRate = parseFloat(rate);
  if (isNaN(numRate)) return '未知';

  // 如果数字大于1，认为已经是百分比数值
  if (numRate > 1) {
    return `${numRate}%`;
  }

  // 如果数字小于等于1，认为是小数，转换为百分比
  return `${(numRate * 100).toFixed(1)}%`;
}
