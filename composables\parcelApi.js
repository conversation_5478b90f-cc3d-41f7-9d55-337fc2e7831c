const ParcelApi = {
  //计算包裹订单信息
  settlementParcel: (data) => {
    console.log('data', data);
    const data2 = {
      ...data,
    };
    // 移除多余字段
    if (!(data.couponId > 0)) {
      delete data2.couponId;
    }
    if (!(data.addressId > 0)) {
      delete data2.addressId;
    }
    if (isEmpty(data.receiverName)) {
      delete data2.receiverName;
    }
    if (isEmpty(data.receiverMobile)) {
      delete data2.receiverMobile;
    }
    // 添加商品项的保险服务
    delete data2.insuranceServices;
    if (data.insuranceServices && data.insuranceServices.length > 0) {
      for (let i = 0; i < data.insuranceServices.length; i++) {
        data2[encodeURIComponent('insuranceServices[' + i + ']')] = data.insuranceServices[i];
      }
    }
    // 添加商品项的免费服务
    delete data2.freeServices;
    if (data.freeServices && data.freeServices.length > 0) {
      for (let i = 0; i < data.freeServices.length; i++) {
        data2[encodeURIComponent('freeServices[' + i + ']')] = data.freeServices[i];
      }
    }

    // 添加商品项的收费服务
    delete data2.chargeServices;
    if (data.chargeServices && data.chargeServices.length > 0) {
      for (let i = 0; i < data.chargeServices.length; i++) {
        data2[encodeURIComponent('chargeServices[' + i + ']')] = data.chargeServices[i];
      }
    }
    // 解决 SpringMVC 接受 List<Item> 参数的问题
    delete data2.items;
    for (let i = 0; i < data.items.length; i++) {
      // data2[encodeURIComponent('items[' + i + '' + '].skuId')] = data.items[i].skuId + '';
      data2[encodeURIComponent('items[' + i + '' + '].count')] = data.items[i].count + '';
      if (data.items[i].stockId) {
        data2[encodeURIComponent('items[' + i + '' + '].stockId')] = data.items[i].stockId + '';
      }
    }
    console.log('data2', data2);
    const queryString = Object.keys(data2)
      .map((key) => key + '=' + data2[key])
      .join('&');
    return useClientGet(`/agent/parcel/settlement?${queryString}`, {
      custom: {
        showError: true,
        showLoading: false,
      },
    });
  },

  // 获取包裹列表
  getParcelPage: (params) => {
    return useClientGet('/agent/parcel/page', {
      params,
      custom: {
        showLoading: false,
      },
    });
  },

  // 获取包裹详情
  getParcelDetail: (id) => {
    return useClientGet(`/agent/parcel/get-detail`, {
      params: { id },
      custom: {
        showLoading: false,
      },
    });
  },

  getParcelCount: () => {
    return useClientGet('/agent/parcel//get-count', {
      custom: {
        showLoading: false,
      },
    });
  },
  getAllCount: () => {
    return useClientGet('/agent/parcel/get-all-count', {
      custom: {
        showLoading: false,
      },
    });
  },

  // 创建包裹
  createParcel: (data) => {
    return useClientPost('/agent/parcel/create', {
      body: data,
    });
  },

  // 取消包裹
  cancelParcel: (id) => {
    return useClientDelete(`/agent/parcel/cancel`, {
      params: { id },
    });
  },

  // 删除包裹
  deleteParcel: (id) => {
    return useClientDelete(`/agent/parcel/delete`, {
      params: { id },
    });
  },
};

export default ParcelApi;
