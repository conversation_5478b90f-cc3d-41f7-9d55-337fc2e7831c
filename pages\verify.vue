<template>
  <HeaderSimple />

  <div class="verify-page">
    <div class="verify-container">
      <!-- 加载状态 -->
      <div v-if="!loaded" class="verify-loading">
        <q-spinner color="primary" size="60px" />
        <div class="verify-loading-text">{{ $t('verify.loading') || 'Verifying your email...' }}</div>
      </div>

      <!-- 验证结果 -->
      <div v-else class="verify-result">
        <!-- 成功验证 -->
        <div v-if="isSuccess" class="verify-success">
          <div class="verify-icon-container">
            <q-icon name="check_circle" color="green" size="80px" />
          </div>

          <h1 class="verify-title">
            {{ $t('verify.success.title') || 'Email Verified Successfully!' }}
          </h1>

          <div class="verify-message">
            {{ $t('verify.success.greeting') || 'Congratulations' }}, <span class="verify-username">{{ userName }}</span
            >! {{ $t('verify.success.message') || 'You have successfully registered on' }} {{ shopName }}.
          </div>

          <div class="verify-coupon">
            <div class="verify-coupon-message">
              {{ $t('verify.success.coupon') || 'A welcome coupon has been sent to your account:' }}
            </div>
            <div class="verify-coupon-value">${{ coupon }}</div>
            <NuxtLink to="/account/coupons" class="verify-coupon-link">
              {{ $t('verify.success.checkCoupon') || 'Click to check your coupons' }}
            </NuxtLink>
          </div>

          <div class="verify-actions">
            <q-btn color="primary" :label="$t('verify.success.startShopping') || 'Begin Shopping Now'" class="verify-action-btn" @click="redirectToHome" />
          </div>

          <div class="verify-countdown">{{ $t('verify.success.redirect') || 'Redirecting to homepage in' }} {{ countdown }} {{ $t('verify.success.seconds') || 'seconds' }}</div>
        </div>

        <!-- 验证失败 -->
        <div v-else class="verify-failed">
          <div class="verify-icon-container">
            <q-icon name="error" color="negative" size="80px" />
          </div>

          <h1 class="verify-title">
            {{ $t('verify.failed.title') || 'Verification Failed' }}
          </h1>

          <div class="verify-message">
            {{ errorMessage || $t('verify.failed.message') || 'The verification link may be invalid or expired. Please try again.' }}
          </div>

          <div class="verify-help">
            {{ $t('verify.failed.help') || 'You can try the following:' }}
            <ul class="verify-help-list">
              <li>{{ $t('verify.failed.checkEmail') || 'Check if you clicked the correct link from your email' }}</li>
              <li>{{ $t('verify.failed.tryAgain') || 'Request a new verification email' }}</li>
              <li>{{ $t('verify.failed.contactSupport') || 'Contact our support team if the problem persists' }}</li>
            </ul>
          </div>

          <div class="verify-actions">
            <q-btn outline color="primary" :label="$t('verify.failed.resend') || 'Resend Verification Email'" class="verify-action-btn" @click="redirectToLogin" />

            <q-btn color="primary" :label="$t('verify.failed.goHome') || 'Go to Homepage'" class="verify-action-btn q-ml-md" @click="redirectToHome" />
          </div>
        </div>
      </div>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router';
import AuthApi from '~/composables/authApi';
import { useAuthStore } from '~/store/auth';
import { useUserStore } from '~/store/user';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const token = route.query.t;

const config = useRuntimeConfig();
const shopName = config.public.shopName;

// 状态变量
const loaded = ref(false);
const isSuccess = ref(false);
const errorMessage = ref('');
const userName = ref('');
const countdown = ref(30);
const coupon = ref(60);

// 验证邮箱
onMounted(async () => {
  try {
    console.log('token:', token);
    if (!token) {
      errorMessage.value = t('verify.failed.invalidLink') || 'Invalid verification link.';
      loaded.value = true;
      return;
    }

    // 调用API验证
    const { code, data } = await AuthApi.validateEmail({ token });

    // 更新状态
    loaded.value = true;

    if (code === 0) {
      // 验证成功
      userName.value = data.userName || 'User';
      isSuccess.value = true;
      startCountdown();

      // 如果已登录，更新用户信息
      if (useAuthStore().isLogin) {
        useUserStore().getUserInfo();
      }
    } else {
      // 验证失败
      isSuccess.value = false;
      errorMessage.value = data?.msg || t('verify.failed.message') || 'Verification failed.';
    }
  } catch (error) {
    console.error('Verification error:', error);
    // 处理异常
    loaded.value = true;
    isSuccess.value = false;
    errorMessage.value = error.response?.data?.message || t('verify.failed.unexpectedError') || 'An unexpected error occurred.';
  }
});

// 倒计时功能
const startCountdown = () => {
  const interval = setInterval(() => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      clearInterval(interval);
      redirectToHome();
    }
  }, 1000);
};

// 跳转到主页
const redirectToHome = () => {
  router.push('/');
};

// 跳转到登录页
const redirectToLogin = () => {
  router.push({
    path: '/login',
    query: { returnTo: '/mail-verify' },
  });
};
</script>

<style lang="scss" scoped>
.verify-page {
  width: 100%;
  padding: 30px 20px;
  min-height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.verify-container {
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
}

/* 加载状态 */
.verify-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;

  .verify-loading-text {
    margin-top: 20px;
    font-size: 1.2rem;
    color: #666;
  }
}

/* 验证结果 */
.verify-result {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 40px;
  text-align: center;

  @media (max-width: 599px) {
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
}

/* 图标容器 */
.verify-icon-container {
  margin-bottom: 25px;

  @media (max-width: 599px) {
    margin-bottom: 20px;
  }
}

/* 标题 */
.verify-title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 25px;
  color: #1976d2;

  @media (max-width: 599px) {
    font-size: 1.5rem;
    margin-bottom: 20px;
  }
}

/* 消息 */
.verify-message {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
  margin-bottom: 30px;

  @media (max-width: 599px) {
    font-size: 1rem;
    margin-bottom: 25px;
  }
}

/* 用户名 */
.verify-username {
  font-weight: 600;
  color: #1976d2;
}

/* 优惠券 */
.verify-coupon {
  background-color: #fff8e1;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;

  @media (max-width: 599px) {
    padding: 15px;
    margin-bottom: 25px;
  }

  .verify-coupon-message {
    margin-bottom: 10px;
    color: #666;
  }

  .verify-coupon-value {
    font-size: 2rem;
    font-weight: 700;
    color: #ff9800;
    margin-bottom: 10px;

    @media (max-width: 599px) {
      font-size: 1.8rem;
    }
  }

  .verify-coupon-link {
    color: #1976d2;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}

/* 操作按钮 */
.verify-actions {
  margin-bottom: 20px;

  @media (max-width: 599px) {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .q-ml-md {
      margin-left: 0 !important;
      margin-top: 10px;
    }
  }

  .verify-action-btn {
    min-width: 200px;
    height: 44px;

    @media (max-width: 599px) {
      width: 100%;
    }
  }
}

/* 倒计时 */
.verify-countdown {
  font-size: 0.9rem;
  color: #666;
}

/* 失败帮助 */
.verify-help {
  text-align: left;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  color: #333;

  @media (max-width: 599px) {
    padding: 15px;
    margin-bottom: 25px;
  }
}

.verify-help-list {
  margin-top: 10px;
  margin-bottom: 0;
  padding-left: 20px;

  li {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
