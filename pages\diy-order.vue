<template>
  <Header />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />

  <div class="diy-order-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <q-icon name="build" size="32px" color="primary" class="q-mr-sm" />
            自定义订单
          </h1>
          <p class="page-subtitle">找不到想要的商品？告诉我们您的需求，我们帮您代购！</p>
        </div>
      </div>
    </div>

    <!-- 操作步骤指南 -->
    <div class="steps-guide">
      <q-card flat bordered class="steps-card">
        <q-card-section class="q-pb-none">
          <div class="steps-header">
            <q-icon name="timeline" size="20px" color="primary" class="q-mr-xs" />
            <span class="text-subtitle1 text-weight-medium">操作流程</span>
          </div>
        </q-card-section>
        <q-card-section>
          <div class="steps-container">
            <div class="step-item" :class="{ active: currentStep >= 1 }">
              <div class="step-circle">
                <q-icon name="edit" size="16px" />
              </div>
              <div class="step-content">
                <div class="step-title">填写商品信息</div>
                <div class="step-desc">提供商品链接和详细要求</div>
              </div>
            </div>
            <div class="step-divider"></div>
            <div class="step-item" :class="{ active: currentStep >= 2 }">
              <div class="step-circle">
                <q-icon name="payment" size="16px" />
              </div>
              <div class="step-content">
                <div class="step-title">确认并支付</div>
                <div class="step-desc">核对信息并完成支付</div>
              </div>
            </div>
            <div class="step-divider"></div>
            <div class="step-item" :class="{ active: currentStep >= 3 }">
              <div class="step-circle">
                <q-icon name="local_shipping" size="16px" />
              </div>
              <div class="step-content">
                <div class="step-title">等待发货</div>
                <div class="step-desc">我们为您采购并发货</div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <CustomOrderForm :initial-data="initialFormData" @submit="handleFormSubmit" @buy-now="handleBuyNow" />
    </div>

    <!-- 服务说明 -->
    <q-card flat bordered class="service-info">
      <q-card-section>
        <div class="service-header">
          <q-icon name="help_outline" size="20px" color="primary" class="q-mr-xs" />
          <span class="text-h6 text-weight-medium">服务说明</span>
        </div>
        <q-separator class="q-my-sm" />

        <div class="service-content">
          <div class="service-item">
            <q-icon name="check_circle" color="positive" class="q-mr-sm" />
            <span>支持淘宝、天猫、1688、京东等主流电商平台代购</span>
          </div>
          <div class="service-item">
            <q-icon name="check_circle" color="positive" class="q-mr-sm" />
            <span>专业客服团队为您提供商品咨询和采购服务</span>
          </div>
          <div class="service-item">
            <q-icon name="check_circle" color="positive" class="q-mr-sm" />
            <span>安全可靠的第三方物流配送服务</span>
          </div>
          <div class="service-item">
            <q-icon name="check_circle" color="positive" class="q-mr-sm" />
            <span>价格透明，无隐藏费用</span>
          </div>
        </div>

        <div class="service-links">
          <!-- <q-btn flat color="primary" icon="book" label="购物代理指南" /> -->
          <q-btn flat color="primary" icon="support_agent" label="联系客服" @click="createTicket" />
          <!-- <q-btn flat color="primary" icon="help" label="常见问题" /> -->
        </div>
      </q-card-section>
    </q-card>
    <!-- 工单弹窗 -->
    <TicketDialog v-model="showTicketDialog" :default-type="TicketApi.TICKET_TYPES.ORDER" :default-title="ticketTitle" @success="onTicketCreated" />
  </div>

  <Footer />
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import CustomOrderForm from '~/components/CustomOrderForm.vue';
import TicketDialog from '~/components/TicketDialog.vue';
import TicketApi from '~/composables/ticketApi';

const router = useRouter();
const route = useRoute();

// 面包屑导航
const breadcrumbs = [{ label: 'DIY订单', to: '/diy-order' }];

// 当前步骤
const currentStep = ref(1);

// 初始表单数据
const initialFormData = ref({
  name: '',
  sourceLink: '',
  specifications: '',
  memo: '',
  price: 0,
  count: 1,
  freight: 0,
});

// 处理表单提交
const handleFormSubmit = (result) => {
  if (result.success) {
    // 跳转到购物车页面
    router.push('/cart');
  }
};

// 处理立即购买
const handleBuyNow = (result) => {
  if (result.success) {
    // 跳转到订单确认页面
    navigateTo('/order/confirm');
  }
};

// 工单弹窗
const showTicketDialog = ref(false);
const ticketTitle = ref('');
const createTicket = () => {
  showTicketDialog.value = true;
  ticketTitle.value = 'DIY Order';
};
const onTicketCreated = (ticket) => {
  console.log('工单创建成功:', ticket);
};
// 页面初始化
onMounted(() => {
  // 如果从搜索页面跳转过来，可能会有预填充的数据

  // 预填充商品链接
  if (route.query.productLink) {
    initialFormData.value.sourceLink = decodeURIComponent(route.query.productLink);
  }

  // 预填充商品名称
  if (route.query.productName) {
    initialFormData.value.name = decodeURIComponent(route.query.productName);
  }
});
</script>

<style lang="scss" scoped>
.diy-order-page {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px 16px;
  background-color: #fafafa;

  @media (max-width: 768px) {
    padding: 12px 8px;
    background-color: #f5f5f5;
  }

  @media (max-width: 480px) {
    padding: 8px 4px;
  }
}

// 页面标题
.page-header {
  margin-bottom: 20px;

  .header-content {
    text-align: center;

    .title-section {
      .page-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin: 0 0 8px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .page-subtitle {
        font-size: 14px;
        color: #666;
        margin: 0;
      }
    }
  }
}

// 步骤指南
.steps-guide {
  margin-bottom: 20px;

  .steps-card {
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

    .steps-header {
      display: flex;
      align-items: center;
    }

    .steps-container {
      display: flex;
      align-items: center;
      justify-content: space-between;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 15px;
      }

      .step-item {
        display: flex;
        align-items: center;
        flex: 1;
        opacity: 0.6;
        transition: all 0.3s ease;

        &.active {
          opacity: 1;

          .step-circle {
            background-color: #0073e6;
            color: white;
          }
        }

        .step-circle {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-color: #e0e0e0;
          color: #666;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          transition: all 0.3s ease;
        }

        .step-content {
          .step-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
            font-size: 14px;
          }

          .step-desc {
            font-size: 11px;
            color: #666;
          }
        }
      }

      .step-divider {
        width: 30px;
        height: 2px;
        background-color: #e0e0e0;
        margin: 0 15px;

        @media (max-width: 768px) {
          width: 2px;
          height: 15px;
          margin: 8px 0;
        }
      }
    }
  }
}

// 表单容器
.form-container {
  .diy-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

// 表单卡片
.form-section {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

  .section-header {
    background-color: #f8f9fa;
    padding: 12px 16px;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
    }
  }
}

// 表单字段
.form-fields {
  padding: 16px;

  .field-group {
    margin-bottom: 16px;

    .field-label {
      display: block;
      font-size: 13px;
      font-weight: 600;
      color: #333;
      margin-bottom: 6px;

      &.required {
        color: #0073e6;
      }
    }

    .form-input {
      width: 100%;

      :deep(.q-field__control) {
        border-radius: 6px;
        min-height: 40px;
      }

      :deep(.q-field__native) {
        font-size: 14px;
      }
    }

    .field-hint {
      display: flex;
      align-items: center;
      margin-top: 4px;
    }

    .field-hints {
      margin-top: 6px;
      display: flex;
      flex-direction: column;
      gap: 3px;
    }

    // 数量选择器
    .quantity-container {
      display: flex;
      align-items: center;
      gap: 0;
      max-width: 160px;

      .quantity-btn {
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        width: 36px;
        height: 36px;

        &:first-child {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
          border-right: none;
        }

        &:last-child {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          border-left: none;
        }
      }

      .quantity-input {
        flex: 1;
        text-align: center;

        :deep(.q-field__control) {
          border-radius: 0;
          min-height: 36px;
        }

        :deep(.q-field__native) {
          text-align: center;
          font-size: 14px;
        }
      }
    }
  }
}

// 费用总计
.total-section {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

  .total-header {
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 12px 16px 0;
  }

  .total-breakdown {
    padding: 0 16px 16px;

    .total-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;

      .total-label {
        color: #666;
        font-size: 13px;
      }

      .total-value {
        color: #333;
        font-weight: 500;
        font-size: 13px;
      }

      &.final-total {
        .total-label {
          color: #333;
          font-weight: 600;
          font-size: 15px;
        }

        .total-amount {
          color: #0073e6;
          font-size: 18px;
          font-weight: bold;
        }
      }
    }
  }

  .total-note {
    display: flex;
    align-items: center;
    margin-top: 8px;
    padding: 0 16px 16px;
  }
}

// 操作按钮
.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;

  @media (max-width: 768px) {
    flex-direction: column;
  }

  .submit-btn,
  .buy-now-btn {
    min-width: 160px;
    height: 42px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    text-transform: none;
  }

  .submit-btn {
    background-color: #0073e6;

    &:hover {
      background-color: #005bb5;
    }
  }

  .buy-now-btn {
    background-color: #ff6b35;

    &:hover {
      background-color: #e55a2b;
    }
  }
}

// 服务信息
.service-info {
  margin-top: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

  .service-header {
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 12px 16px 0;
  }

  .service-content {
    padding: 0 16px;

    .service-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 13px;
      color: #333;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .service-links {
    display: flex;
    gap: 8px;
    margin-top: 16px;
    padding: 0 16px 16px;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .diy-order-page {
    padding: 12px 8px;
  }

  .page-header {
    margin-bottom: 16px;

    .header-content {
      .title-section {
        .page-title {
          font-size: 20px;
          flex-direction: column;
          gap: 8px;
        }

        .page-subtitle {
          font-size: 13px;
        }
      }
    }
  }

  .form-fields {
    padding: 12px;

    .field-group {
      margin-bottom: 14px;

      .quantity-container {
        max-width: 100%;
      }
    }
  }

  .action-buttons {
    margin-top: 16px;

    .submit-btn,
    .buy-now-btn {
      min-width: 100%;
      height: 44px;
    }
  }
}

@media (max-width: 480px) {
  .diy-order-page {
    padding: 10px 6px;
  }

  .page-header {
    .header-content {
      .title-section {
        .page-title {
          font-size: 18px;
        }

        .page-subtitle {
          font-size: 12px;
        }
      }
    }
  }

  .form-fields {
    padding: 10px;

    .field-group {
      margin-bottom: 12px;

      .field-label {
        font-size: 12px;
      }

      .form-input {
        :deep(.q-field__control) {
          min-height: 36px;
        }

        :deep(.q-field__native) {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
