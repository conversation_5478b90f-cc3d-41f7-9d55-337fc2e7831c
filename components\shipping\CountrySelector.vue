<template>
  <div class="country-selector">
    <div class="country-input-wrapper" @click="openCountryDialog">
      <q-field v-model="selectedCountryDisplay" :label="label" dense outlined :error="error" :error-message="errorMessage" class="cursor-pointer">
        <template #prepend>
          <q-icon name="public" />
        </template>

        <template #control>
          <div class="self-center full-width no-outline" tabindex="0">
            <div v-if="selectedCountryInfo" class="row items-center q-gutter-sm">
              <img v-if="selectedCountryInfo.flagUrl" :src="selectedCountryInfo.flagUrl" :alt="selectedCountryInfo.name" class="country-flag-small" @error="onImageError" />
              <span>{{ selectedCountryInfo.name }}</span>
              <span class="text-grey-6">({{ selectedCountryInfo.nameEn }})</span>
            </div>
            <!-- <div v-else class="text-grey-7">
              {{ $t('shippingCalculator.selectCountry', '选择国家') }}
            </div> -->
          </div>
        </template>

        <template #append>
          <q-icon name="arrow_drop_down" />
        </template>
      </q-field>
    </div>

    <!-- 国家选择对话框 -->
    <q-dialog v-model="showCountryDialog" class="country-dialog" :transition-show="null" :transition-hide="null">
      <q-card style="width: 700px; max-width: 95vw">
        <q-card-section class="q-pa-sm">
          <div class="dialog-header">
            <div class="text-h6">{{ $t('shippingCalculator.selectDestination', '目的地') }}</div>
            <q-btn flat round dense icon="close" size="sm" @click="showCountryDialog = false" class="close-btn" />
          </div>

          <!-- 搜索框 -->
          <q-input v-model="searchText" outlined dense :placeholder="$t('common.searchCountry', '搜索国家或地区')" clearable class="q-mb-sm">
            <template #prepend>
              <q-icon name="search" size="sm" />
            </template>
          </q-input>

          <!-- 标签页 -->
          <q-tabs v-model="activeTab" dense class="text-grey country-tabs" active-color="primary" indicator-color="primary" align="justify" narrow-indicator>
            <q-tab name="hot" label="HOT" class="text-xs" />
            <q-tab name="abc" label="ABC" class="text-xs" />
            <q-tab name="def" label="DEF" class="text-xs" />
            <q-tab name="ghi" label="GHI" class="text-xs" />
            <q-tab name="jkl" label="JKL" class="text-xs" />
            <q-tab name="mno" label="MNO" class="text-xs" />
            <q-tab name="pqr" label="PQR" class="text-xs" />
            <q-tab name="stu" label="STU" class="text-xs" />
            <q-tab name="vwy" label="VWY" class="text-xs" />
            <q-tab name="z" label="Z" class="text-xs" />
          </q-tabs>
        </q-card-section>

        <q-card-section class="q-pa-sm country-content">
          <q-tab-panels v-model="activeTab" class="country-panels">
            <!-- 热门国家 -->
            <q-tab-panel name="hot" class="q-pa-none">
              <div class="country-grid">
                <div v-for="country in hotCountries" :key="country.code" class="country-item" @click="selectCountry(country)" :class="{ selected: selectedCountry === country.code }">
                  <div class="country-flag-container">
                    <img v-if="country.flagUrl" :src="country.flagUrl" :alt="country.name" class="country-flag-dialog" @error="onImageError" />
                  </div>
                  <div class="country-text">
                    <div class="country-name">{{ country.name }}</div>
                    <div v-if="country.name !== country.nameEn" class="country-name-en">{{ country.nameEn }}</div>
                  </div>
                </div>
              </div>
            </q-tab-panel>

            <!-- 字母分组 -->
            <q-tab-panel v-for="tab in alphabetTabs" :key="tab.name" :name="tab.name" class="q-pa-none">
              <div class="country-grid">
                <div
                  v-for="country in getCountriesByLetter(tab.letters)"
                  :key="country.code"
                  class="country-item"
                  @click="selectCountry(country)"
                  :class="{ selected: selectedCountry === country.code }">
                  <div class="country-flag-container">
                    <img v-if="country.flagUrl" :src="country.flagUrl" :alt="country.name" class="country-flag-dialog" @error="onImageError" />
                  </div>
                  <div class="country-text">
                    <div class="country-name">{{ country.name }}</div>
                    <div v-if="country.name !== country.nameEn" class="country-name-en">{{ country.nameEn }}</div>
                  </div>
                </div>
              </div>
            </q-tab-panel>
          </q-tab-panels>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import CountryApi from '~/composables/countryApi';

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
  label: {
    type: String,
    default: '选择国家',
  },
  error: {
    type: Boolean,
    default: false,
  },
  errorMessage: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);

const { t } = useI18n();

// 状态管理
const loading = ref(false);
const countries = ref([]);
const selectedCountry = ref(props.modelValue);
const showCountryDialog = ref(false);
const activeTab = ref('hot');
const searchText = ref('');

// 字母分组配置
const alphabetTabs = [
  { name: 'abc', letters: ['A', 'B', 'C'] },
  { name: 'def', letters: ['D', 'E', 'F'] },
  { name: 'ghi', letters: ['G', 'H', 'I'] },
  { name: 'jkl', letters: ['J', 'K', 'L'] },
  { name: 'mno', letters: ['M', 'N', 'O'] },
  { name: 'pqr', letters: ['P', 'Q', 'R'] },
  { name: 'stu', letters: ['S', 'T', 'U'] },
  { name: 'vwy', letters: ['V', 'W', 'Y'] },
  { name: 'z', letters: ['Z'] },
];

// 计算属性
const selectedCountryInfo = computed(() => {
  if (!selectedCountry.value) return null;
  return countries.value.find((country) => country.code === selectedCountry.value);
});

const selectedCountryDisplay = computed(() => {
  return selectedCountryInfo.value?.name || '';
});

const hotCountries = computed(() => {
  return countries.value.filter((country) => country.hot).sort((a, b) => (b.sort || 0) - (a.sort || 0));
});

const filteredCountries = computed(() => {
  if (!searchText.value) return countries.value;

  const needle = searchText.value.toLowerCase();
  return countries.value.filter((country) => country.name.toLowerCase().includes(needle) || country.nameEn.toLowerCase().includes(needle) || country.code.toLowerCase().includes(needle));
});

// 根据字母获取国家
const getCountriesByLetter = (letters) => {
  const filtered = searchText.value ? filteredCountries.value : countries.value;
  return filtered
    .filter((country) => {
      const firstLetter = country.nameEn.charAt(0).toUpperCase();
      return letters.includes(firstLetter);
    })
    .sort((a, b) => a.nameEn.localeCompare(b.nameEn));
};

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    selectedCountry.value = newValue;
  }
);

// 监听选中值变化
watch(selectedCountry, (newValue) => {
  emit('update:modelValue', newValue);
});

// 获取国家列表
const fetchCountries = async () => {
  loading.value = true;
  try {
    const response = await CountryApi.getList();
    if (response.code === 0 && response.data) {
      countries.value = response.data.map((country) => ({
        ...country,
        // 确保flagUrl是完整的URL
        flagUrl: country.flagUrl ? (country.flagUrl.startsWith('http') ? country.flagUrl : `${window.location.origin}${country.flagUrl}`) : null,
      }));
    }
  } catch (error) {
    console.error('获取国家列表失败:', error);
    // 可以在这里添加错误提示
  } finally {
    loading.value = false;
  }
};

// 选择国家
const selectCountry = (country) => {
  selectedCountry.value = country.code;
  emit('update:modelValue', country.code);
  showCountryDialog.value = false;
};

// 打开国家选择对话框
const openCountryDialog = () => {
  console.log('Opening country dialog...', showCountryDialog.value);
  showCountryDialog.value = true;
  console.log('After setting:', showCountryDialog.value);
};

// 图片加载错误处理
const onImageError = (event) => {
  event.target.style.display = 'none';
};

// 组件挂载时获取数据
onMounted(() => {
  fetchCountries();
});
</script>

<style lang="scss" scoped>
.country-selector {
  .country-input-wrapper {
    cursor: pointer;
  }

  .country-flag-small {
    width: 20px;
    height: 14px;
    object-fit: cover;
    border-radius: 2px;
    border: 1px solid #e0e0e0;
  }

  .country-flag-dialog {
    width: 24px;
    height: 16px;
    object-fit: cover;
    border-radius: 2px;
    border: 1px solid #e0e0e0;
    display: block;
  }
}

.country-dialog {
  .country-tabs {
    .q-tab {
      min-height: 32px;
      font-size: 11px;
      padding: 4px 8px;
    }
  }

  .country-content {
    height: 300px;
    overflow: hidden;
  }

  .country-panels {
    height: 100%;

    .q-tab-panel {
      padding: 0;
      height: 100%;
    }
  }

  .country-grid {
    height: 100%;
    overflow-y: auto;
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    align-content: start;
  }

  .country-item {
    display: flex;
    align-items: center;
    padding: 8px 6px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    background: white;
    min-height: 50px;

    &:hover {
      border-color: #1976d2;
      box-shadow: 0 1px 4px rgba(25, 118, 210, 0.2);
    }

    &.selected {
      border-color: #1976d2;
      background-color: #e3f2fd;
      box-shadow: 0 1px 4px rgba(25, 118, 210, 0.3);
    }
  }

  .country-flag-container {
    width: 32px;
    height: 20px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    flex-shrink: 0;
    border: 1px solid #e0e0e0;
    border-radius: 2px;
  }

  .country-flag-dialog {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .close-btn {
    color: #666;

    &:hover {
      color: #333;
      background-color: rgba(0, 0, 0, 0.04);
    }
  }

  .country-text {
    flex: 1;
    min-width: 0;
  }

  .country-name {
    font-size: 12px;
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 1px;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  .country-name-en {
    font-size: 10px;
    color: #666;
    line-height: 1.1;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
}

:deep(.q-field__control) {
  cursor: pointer;

  .q-field__native {
    padding-left: 8px;
  }
}

:deep(.q-tabs) {
  .q-tab {
    min-height: 40px;
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .country-dialog {
    :deep(.q-dialog__inner) {
      padding: 8px;
    }

    .q-card {
      height: 85vh !important;
      max-height: 85vh !important;
      width: 100% !important;
      max-width: 100% !important;
      margin: 0;
    }

    .country-content {
      height: calc(85vh - 120px) !important;
    }

    .country-panels {
      height: 100% !important;
    }

    .country-grid {
      height: 100% !important;
      grid-template-columns: repeat(2, 1fr) !important;
      gap: 6px !important;
      padding: 6px !important;
      overflow-x: hidden !important;
    }

    .country-item {
      min-height: 60px !important;
      padding: 6px 4px !important;
    }

    .country-flag-container {
      width: 28px !important;
      height: 18px !important;
      margin-right: 6px !important;
      flex-shrink: 0;
    }

    .country-text {
      min-width: 0;
      flex: 1;
    }

    .country-name {
      font-size: 11px !important;
    }

    .country-name-en {
      font-size: 9px !important;
    }

    .country-card {
      .text-subtitle2 {
        font-size: 0.8rem;
      }

      .text-caption {
        font-size: 0.7rem;
      }
    }

    :deep(.q-tabs) {
      .q-tab {
        min-height: 32px !important;
        font-size: 10px !important;
        padding: 4px 6px !important;
      }
    }
  }
}
</style>
