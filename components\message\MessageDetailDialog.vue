<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card style="min-width: 400px; max-width: 600px; width: 90vw">
      <q-card-section class="row items-center bg-primary text-white">
        <q-icon :name="getMessageIcon(message.templateType)" size="md" class="q-mr-sm" />
        <div class="text-h6">消息详情</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup color="white" />
      </q-card-section>

      <q-separator />

      <q-card-section class="q-pa-lg">
        <div class="row items-center q-mb-md">
          <q-badge :color="getMessageColor(message.templateType)" :label="getMessageTypeName(message.templateType)" />
          <q-space />
          <span class="text-caption text-grey-7">{{ formatDateTime(message.createTime) }}</span>
        </div>

        <div class="message-content">
          <div class="text-body1" style="line-height: 1.6; white-space: pre-wrap;">
            {{ message.templateContent }}
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right" class="q-pa-md">
        <q-btn flat label="关闭" color="primary" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, watch } from 'vue';
import { date } from 'quasar';

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  message: {
    type: Object,
    default: () => ({}),
  },
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'messageRead']);

// 内部状态
const showDialog = ref(false);

// 监听外部传入的显示状态
watch(() => props.modelValue, (newVal) => {
  showDialog.value = newVal;
});

// 监听内部状态变化，同步到外部
watch(showDialog, (newVal) => {
  emit('update:modelValue', newVal);
  
  // 当弹窗打开且消息未读时，触发已读事件
  if (newVal && props.message && !props.message.readStatus) {
    emit('messageRead', props.message);
  }
});

// 获取消息类型图标
const getMessageIcon = (templateType) => {
  const icons = {
    1: 'notifications', // 通知消息
    2: 'info', // 系统消息
  };
  return icons[templateType] || 'mail';
};

// 获取消息类型颜色
const getMessageColor = (templateType) => {
  const colors = {
    1: 'green', // 通知消息
    2: 'blue', // 系统消息
  };
  return colors[templateType] || 'grey';
};

// 获取消息类型名称
const getMessageTypeName = (templateType) => {
  const names = {
    1: '通知消息',
    2: '系统消息',
  };
  return names[templateType] || '未知类型';
};

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';
  return date.formatDate(new Date(timestamp), 'YYYY-MM-DD HH:mm');
};
</script>

<style lang="scss" scoped>
.message-content {
  white-space: pre-line;
  line-height: 1.6;
}
</style>
