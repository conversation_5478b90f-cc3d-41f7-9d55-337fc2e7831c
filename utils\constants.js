export const BannerTag = {
  TOPAD: 'top', //顶部广告
  EVENT: 'event', //活动
  BLOG: 'blog', //活动
};

/**
 * 促销展位位置
 */
export const PromotionPosition = {
  RECOMMEND: { positionId: 1, title: 'Recommend' }, //推荐
  NEW: { positionId: 2, title: 'New' }, //新品
  HOT: { positionId: 3, title: 'Hot' }, //最佳
  ONSALE: { positionId: 4, title: 'Onsale' }, //特价
  SIDEBAR_NEW: { positionId: 5, title: 'New' }, //侧边新品
  SIDEBAR_HOT: { positionId: 6, title: 'Hot' }, //侧边最佳
  GRID_6: { positionId: 7, title: 'Grid6' }, //六宫格
};

/**
 * 文案类型
 */
export const ArticleCategory = {
  BLOG: 4, //博客
};

export const SortField = {
  PRICE: 'price', //价格
  SALES_COUNT: 'salesCount', //销量 hot
  CREATE_TIME: 'createTime', //新品
  ALPHABETIC: 'alphabetic', //字典排序
  SCORE: 'score', //评分
};

export const PayOrderSources = {
  PARCEL: 'p', //包裹订单
  ORDER: 'o', //购物订单
  CHARGE: 'c', //充值订单
  SUPPLEMENT: 's', //充值订单
};

/**
 * 售后状态枚举
 */
export const AfterSaleStatusEnum = {
  APPLY: 10, // 申请中
  SELLER_AGREE: 20, // 卖家通过
  BUYER_DELIVERY: 30, // 待卖家收货
  WAIT_REFUND: 40, // 等待平台退款
  COMPLETE: 50, // 完成
  BUYER_CANCEL: 61, // 买家取消售后
  SELLER_DISAGREE: 62, // 卖家拒绝
  SELLER_REFUSE: 63, // 卖家拒绝收货
};

/**
 * 售后方式枚举
 */
export const AfterSaleWayEnum = {
  REFUND: 10, // 仅退款
  RETURN_AND_REFUND: 20, // 退货退款
};

/**
 * 售后类型枚举
 */
export const AfterSaleTypeEnum = {
  IN_SALE: 10, // 售中退款
  AFTER_SALE: 20, // 售后退款
};

export const ArticleCategoryTypeEnum = {
  ACTIVITIES: 5, //活动
  BLOG: 6, //博客
};

export const SearchPlatforms = [
  { name: 'taobao', labelKey: '淘宝/天猫' },
  { name: '1688', labelKey: '1688' },
  // { name: 'jd', labelKey: '京东' },
  // { name: 'pdd', labelKey: '拼多多' },
  // { name: 'micro', labelKey: '微店' },
  // { name: 'vip', labelKey: '唯品会' },
  // { name: 'goodfish', labelKey: '闲鱼' },
];

export const PLATFORM_NAME = 'cnitems';

// 国际电话区号选项
export const phoneCodeOptions = [
  { label: '+1', value: '+1' },
  { label: '+7', value: '+7' },
  { label: '+20', value: '+20' },
  { label: '+27', value: '+27' },
  { label: '+30', value: '+30' },
  { label: '+31', value: '+31' },
  { label: '+32', value: '+32' },
  { label: '+33', value: '+33' },
  { label: '+34', value: '+34' },
  { label: '+36', value: '+36' },
  { label: '+39', value: '+39' },
  { label: '+40', value: '+40' },
  { label: '+41', value: '+41' },
  { label: '+43', value: '+43' },
  { label: '+44', value: '+44' },
  { label: '+45', value: '+45' },
  { label: '+46', value: '+46' },
  { label: '+47', value: '+47' },
  { label: '+48', value: '+48' },
  { label: '+49', value: '+49' },
  { label: '+51', value: '+51' },
  { label: '+52', value: '+52' },
  { label: '+53', value: '+53' },
  { label: '+54', value: '+54' },
  { label: '+55', value: '+55' },
  { label: '+56', value: '+56' },
  { label: '+57', value: '+57' },
  { label: '+58', value: '+58' },
  { label: '+60', value: '+60' },
  { label: '+61', value: '+61' },
  { label: '+62', value: '+62' },
  { label: '+63', value: '+63' },
  { label: '+64', value: '+64' },
  { label: '+65', value: '+65' },
  { label: '+66', value: '+66' },
  { label: '+81', value: '+81' },
  { label: '+82', value: '+82' },
  { label: '+84', value: '+84' },
  { label: '+86', value: '+86' },
  { label: '+211', value: '+211' },
  { label: '+212', value: '+212' },
  { label: '+213', value: '+213' },
  { label: '+216', value: '+216' },
  { label: '+218', value: '+218' },
  { label: '+220', value: '+220' },
  { label: '+221', value: '+221' },
  { label: '+222', value: '+222' },
  { label: '+223', value: '+223' },
  { label: '+224', value: '+224' },
  { label: '+225', value: '+225' },
  { label: '+226', value: '+226' },
  { label: '+227', value: '+227' },
  { label: '+228', value: '+228' },
  { label: '+229', value: '+229' },
  { label: '+230', value: '+230' },
  { label: '+231', value: '+231' },
  { label: '+232', value: '+232' },
  { label: '+233', value: '+233' },
  { label: '+234', value: '+234' },
  { label: '+235', value: '+235' },
  { label: '+236', value: '+236' },
  { label: '+237', value: '+237' },
  { label: '+238', value: '+238' },
  { label: '+239', value: '+239' },
  { label: '+240', value: '+240' },
  { label: '+241', value: '+241' },
  { label: '+242', value: '+242' },
  { label: '+243', value: '+243' },
  { label: '+244', value: '+244' },
  { label: '+245', value: '+245' },
  { label: '+246', value: '+246' },
  { label: '+247', value: '+247' },
  { label: '+248', value: '+248' },
  { label: '+249', value: '+249' },
  { label: '+250', value: '+250' },
  { label: '+251', value: '+251' },
  { label: '+252', value: '+252' },
  { label: '+253', value: '+253' },
  { label: '+254', value: '+254' },
  { label: '+255', value: '+255' },
  { label: '+256', value: '+256' },
  { label: '+257', value: '+257' },
  { label: '+258', value: '+258' },
  { label: '+260', value: '+260' },
  { label: '+261', value: '+261' },
  { label: '+262', value: '+262' },
  { label: '+263', value: '+263' },
  { label: '+264', value: '+264' },
  { label: '+265', value: '+265' },
  { label: '+266', value: '+266' },
  { label: '+267', value: '+267' },
  { label: '+268', value: '+268' },
  { label: '+269', value: '+269' },
  { label: '+290', value: '+290' },
  { label: '+291', value: '+291' },
  { label: '+297', value: '+297' },
  { label: '+298', value: '+298' },
  { label: '+299', value: '+299' },
  { label: '+350', value: '+350' },
  { label: '+351', value: '+351' },
  { label: '+352', value: '+352' },
  { label: '+353', value: '+353' },
  { label: '+354', value: '+354' },
  { label: '+355', value: '+355' },
  { label: '+356', value: '+356' },
  { label: '+357', value: '+357' },
  { label: '+358', value: '+358' },
  { label: '+359', value: '+359' },
  { label: '+370', value: '+370' },
  { label: '+371', value: '+371' },
  { label: '+372', value: '+372' },
  { label: '+373', value: '+373' },
  { label: '+374', value: '+374' },
  { label: '+375', value: '+375' },
  { label: '+376', value: '+376' },
  { label: '+377', value: '+377' },
  { label: '+378', value: '+378' },
  { label: '+380', value: '+380' },
  { label: '+381', value: '+381' },
  { label: '+382', value: '+382' },
  { label: '+385', value: '+385' },
  { label: '+386', value: '+386' },
  { label: '+387', value: '+387' },
  { label: '+389', value: '+389' },
  { label: '+420', value: '+420' },
  { label: '+421', value: '+421' },
  { label: '+423', value: '+423' },
  { label: '+500', value: '+500' },
  { label: '+501', value: '+501' },
  { label: '+502', value: '+502' },
  { label: '+503', value: '+503' },
  { label: '+504', value: '+504' },
  { label: '+505', value: '+505' },
  { label: '+506', value: '+506' },
  { label: '+507', value: '+507' },
  { label: '+508', value: '+508' },
  { label: '+509', value: '+509' },
  { label: '+590', value: '+590' },
  { label: '+591', value: '+591' },
  { label: '+592', value: '+592' },
  { label: '+593', value: '+593' },
  { label: '+594', value: '+594' },
  { label: '+595', value: '+595' },
  { label: '+596', value: '+596' },
  { label: '+597', value: '+597' },
  { label: '+598', value: '+598' },
  { label: '+599', value: '+599' },
  { label: '+670', value: '+670' },
  { label: '+673', value: '+673' },
  { label: '+674', value: '+674' },
  { label: '+675', value: '+675' },
  { label: '+676', value: '+676' },
  { label: '+677', value: '+677' },
  { label: '+678', value: '+678' },
  { label: '+679', value: '+679' },
  { label: '+680', value: '+680' },
  { label: '+681', value: '+681' },
  { label: '+682', value: '+682' },
  { label: '+683', value: '+683' },
  { label: '+685', value: '+685' },
  { label: '+686', value: '+686' },
  { label: '+687', value: '+687' },
  { label: '+688', value: '+688' },
  { label: '+689', value: '+689' },
  { label: '+690', value: '+690' },
  { label: '+691', value: '+691' },
  { label: '+692', value: '+692' },
  { label: '+800', value: '+800' },
  { label: '+808', value: '+808' },
  { label: '+850', value: '+850' },
  { label: '+852', value: '+852' },
  { label: '+853', value: '+853' },
  { label: '+855', value: '+855' },
  { label: '+856', value: '+856' },
  { label: '+875', value: '+875' },
  { label: '+876', value: '+876' },
  { label: '+877', value: '+877' },
  { label: '+878', value: '+878' },
  { label: '+879', value: '+879' },
  { label: '+880', value: '+880' },
  { label: '+881', value: '+881' },
  { label: '+882', value: '+882' },
  { label: '+1242', value: '+1242' },
  { label: '+1246', value: '+1246' },
  { label: '+1264', value: '+1264' },
  { label: '+1268', value: '+1268' },
  { label: '+1284', value: '+1284' },
  { label: '+1340', value: '+1340' },
  { label: '+1345', value: '+1345' },
  { label: '+1441', value: '+1441' },
  { label: '+1473', value: '+1473' },
  { label: '+1649', value: '+1649' },
  { label: '+1664', value: '+1664' },
  { label: '+1670', value: '+1670' },
  { label: '+1671', value: '+1671' },
  { label: '+1684', value: '+1684' },
  { label: '+1721', value: '+1721' },
  { label: '+1758', value: '+1758' },
  { label: '+1767', value: '+1767' },
  { label: '+1784', value: '+1784' },
  { label: '+1787', value: '+1787' },
  { label: '+1809', value: '+1809' },
  { label: '+1829', value: '+1829' },
  { label: '+1849', value: '+1849' },
  { label: '+1868', value: '+1868' },
  { label: '+1869', value: '+1869' },
  { label: '+1876', value: '+1876' },
  { label: '+1939', value: '+1939' },
  { label: '+4428', value: '+4428' },
  { label: '+6721', value: '+6721' },
  { label: '+6723', value: '+6723' },
  { label: '+7840', value: '+7840' },
  { label: '+8811', value: '+8811' },
  { label: '+8812', value: '+8812' },
  { label: '+8813', value: '+8813' },
  { label: '+8816', value: '+8816' },
  { label: '+8817', value: '+8817' },
  { label: '+8818', value: '+8818' },
  { label: '+8819', value: '+8819' },
  { label: '+35818', value: '+35818' },
  { label: '+37447', value: '+37447' },
  { label: '+88210', value: '+88210' },
  { label: '+88212', value: '+88212' },
  { label: '+88213', value: '+88213' },
  { label: '+88215', value: '+88215' },
  { label: '+88216', value: '+88216' },
  { label: '+88220', value: '+88220' },
  { label: '+88222', value: '+88222' },
  { label: '+88223', value: '+88223' },
  { label: '+88224', value: '+88224' },
  { label: '+88230', value: '+88230' },
  { label: '+88231', value: '+88231' },
  { label: '+88232', value: '+88232' },
  { label: '+88233', value: '+88233' },
  { label: '+88234', value: '+88234' },
  { label: '+88235', value: '+88235' },
  { label: '+88236', value: '+88236' },
  { label: '+88237', value: '+88237' },
  { label: '+88239', value: '+88239' },
  { label: '+88240', value: '+88240' },
  { label: '+88241', value: '+88241' },
  { label: '+88242', value: '+88242' },
  { label: '+88243', value: '+88243' },
  { label: '+441481', value: '+441481' },
  { label: '+441534', value: '+441534' },
  { label: '+441624', value: '+441624' },
  { label: '+3906698', value: '+3906698' },
  { label: '+6189162', value: '+6189162' },
  { label: '+6189164', value: '+6189164' },
];
