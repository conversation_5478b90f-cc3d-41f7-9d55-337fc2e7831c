<template>
  <!-- 页脚 -->
  <footer class="footer">
    <div class="footer-main">
      <div class="footer-content">
        <!-- 在移动端使用折叠面板 -->
        <div class="lt-md mobile-footer">
          <q-expansion-item
            v-for="(section, index) in 5"
            :key="index"
            :label="$tm(`footer.title${index + 1}`)"
            header-class="footer-expansion-header"
            expand-icon-class="text-white"
            expand-icon="keyboard_arrow_down"
            dense>
            <q-card class="footer-card">
              <q-card-section class="footer-expansion-content">
                <div class="footer-links-row">
                  <q-btn v-for="(link, linkIndex) in $tm(`footer.links${index + 1}`)" :key="linkIndex" flat no-caps dense class="footer-link-btn" :label="link" />
                </div>
              </q-card-section>
            </q-card>
          </q-expansion-item>
        </div>

        <!-- 桌面端显示完整菜单 -->
        <div class="gt-sm desktop-footer">
          <div class="footer-section" v-for="(section, index) in 5" :key="index">
            <h3 class="footer-title">{{ $tm(`footer.title${index + 1}`) }}</h3>
            <ul class="footer-links">
              <li v-for="(link, linkIndex) in $tm(`footer.links${index + 1}`)" :key="linkIndex">
                {{ link }}
              </li>
            </ul>
          </div>
        </div>

        <div class="footer-qr gt-xs">
          <div class="qr-item">
            <img src="/images/app-qr.png" alt="App QR Code" />
            <p>{{ $t('mobileApp') }}</p>
          </div>
          <div class="qr-item">
            <img src="/images/wechat-qr.png" alt="WeChat QR Code" />
            <p>{{ $t('wechatOfficial') }}</p>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <div class="payment-icons">
          <div class="payment-icon">
            <img src="/images/paypal.png" alt="Paypal" />
          </div>
          <div class="payment-icon">
            <img src="/images/stripe.png" alt="Stripe" />
          </div>
          <div class="payment-icon">
            <img src="/images/alipay.png" alt="Alipay" />
          </div>
          <div class="payment-icon">
            <img src="/images/visa.png" alt="Visa" />
          </div>
          <div class="payment-icon">
            <img src="/images/mastercard.png" alt="MasterCard" />
          </div>
          <div class="payment-icon">
            <img src="/images/discover.png" alt="Discover" />
          </div>
        </div>

        <div class="copyright-container">
          <p class="copyright">Copyright ©2025 CnItems Inc. All rights reserved.</p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { useResponsive } from '~/composables/useResponsive';

const { isMobile, isTablet, isDesktop } = useResponsive();
</script>

<style lang="scss" scoped>
//footer
.footer {
  width: 100%;
  background-color: #333;
  color: #ccc;
  padding: 40px 0px;
  font-size: 14px;

  @media (max-width: 767px) {
    padding: 20px 0;
  }

  .footer-main {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 0 15px;

    .footer-content {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 30px;

      @media (max-width: 767px) {
        margin-bottom: 20px;
      }

      // 移动端折叠面板样式
      .mobile-footer {
        width: 100%;

        .footer-expansion-header {
          color: #fff !important;
          font-weight: bold;
          font-size: 16px;
          border-bottom: 1px solid #444;
          padding: 12px 16px;

          &:hover {
            background-color: #444;
          }
        }

        .footer-card {
          background-color: #2a2a2a;
          border: none;
          box-shadow: none;
        }

        .footer-expansion-content {
          background-color: #2a2a2a;
          padding: 10px;
        }

        .footer-links-row {
          display: flex;
          flex-wrap: wrap;
          margin: -4px;
        }

        .footer-link-btn {
          color: #ccc;
          font-size: 13px;
          margin: 4px;
          border-radius: 4px;
          min-height: 32px;

          &:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
          }
        }
      }

      // 桌面端菜单样式
      .desktop-footer {
        display: flex;
        flex-wrap: wrap;
        width: 75%;

        @media (max-width: 1023px) {
          width: 70%;
        }
      }

      .footer-section {
        flex: 1;
        min-width: 150px;
        margin-bottom: 20px;

        @media (max-width: 1023px) {
          min-width: 120px;
        }

        .footer-title {
          font-size: 16px;
          font-weight: bold;
          color: #fff;
          margin-bottom: 15px;
        }

        .footer-links {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            margin-bottom: 5px;
            color: #ccc;

            &:hover {
              color: #fff;
              cursor: pointer;
            }
          }
        }
      }

      .footer-qr {
        display: flex;
        gap: 20px;

        @media (max-width: 1023px) {
          gap: 10px;
        }

        .qr-item {
          text-align: center;

          img {
            width: 80px;
            height: 80px;
            margin-bottom: 5px;

            @media (max-width: 1023px) {
              width: 70px;
              height: 70px;
            }
          }

          p {
            color: #ccc;
            font-size: 12px;
          }
        }
      }
    }

    .footer-bottom {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      border-top: 1px solid #444;
      padding-top: 25px;
      margin-top: 10px;

      .payment-icons {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
        justify-content: center;

        .payment-icon {
          background-color: #fff;
          border-radius: 6px;
          padding: 6px 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: transform 0.2s;

          &:hover {
            transform: translateY(-3px);
          }

          img {
            width: 40px;
            height: 25px;
            object-fit: contain;

            @media (max-width: 599px) {
              width: 35px;
              height: 22px;
            }
          }
        }
      }

      .copyright-container {
        background-color: rgba(0, 0, 0, 0.2);
        width: 100%;
        padding: 15px 0;
        margin-top: 10px;
        border-radius: 4px;
      }

      .copyright {
        font-size: 12px;
        color: #777;
        text-align: center;
        margin: 0;
      }
    }
  }
}
</style>
