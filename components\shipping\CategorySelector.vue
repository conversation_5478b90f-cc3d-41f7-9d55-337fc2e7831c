<template>
  <div class="category-selector">
    <div class="category-input-wrapper" @click="openCategoryDialog">
      <q-field :model-value="selectedCategoriesDisplay" :label="label" dense outlined :error="error" :error-message="errorMessage" class="cursor-pointer">
        <template #prepend>
          <q-icon name="category" />
        </template>

        <template #control>
          <div class="self-center full-width no-outline" tabindex="0">
            <div v-if="selectedCategories.length > 0" class="row items-center q-gutter-xs flex-wrap">
              <q-chip
                v-for="category in selectedCategories.slice(0, 2)"
                :key="`${category.mainId}-${category.subId}`"
                color="primary"
                text-color="white"
                size="sm"
                dense
                removable
                @remove="() => removeCategory(category)">
                {{ category.mainName }} / {{ category.subName }}
              </q-chip>
              <q-chip v-if="selectedCategories.length > 2" color="grey-5" text-color="white" dense> +{{ selectedCategories.length - 2 }} </q-chip>
            </div>
            <!-- <div v-else class="text-grey-7">
              {{ $t('shippingCalculator.selectCategory', '选择产品类别') }}
            </div> -->
          </div>
        </template>

        <template #append>
          <q-icon name="arrow_drop_down" />
        </template>
      </q-field>
    </div>

    <!-- 分类选择对话框 -->
    <q-dialog v-model="showCategoryDialog" class="category-dialog" :transition-show="null" :transition-hide="null">
      <q-card style="width: 800px; max-width: 95vw">
        <q-card-section class="q-pa-sm">
          <div class="dialog-header">
            <div class="text-h6">{{ $t('shippingCalculator.productCategory', '产品类别') }}</div>
            <q-btn flat round dense icon="close" size="sm" @click="showCategoryDialog = false" class="close-btn" />
          </div>
        </q-card-section>

        <q-card-section class="q-pa-sm category-content">
          <div class="category-layout">
            <!-- 主分类列表 -->
            <div class="main-category-section">
              <div class="category-title">{{ $t('common.mainCategory', '主分类') }}</div>
              <div class="main-category-container">
                <div class="main-category-list">
                  <div
                    v-for="category in filteredMainCategories"
                    :key="category.id"
                    class="main-category-item"
                    :class="{ active: selectedMainCategory === category.id }"
                    @click="selectMainCategory(category)">
                    <div class="category-item-content">
                      <div class="category-name">{{ category.name }}</div>
                    </div>
                    <q-icon name="chevron_right" size="sm" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 子分类列表 -->
            <div class="sub-category-section">
              <div class="category-title">
                {{ selectedMainCategoryInfo?.name || $t('common.subCategory', '子分类') }}
              </div>
              <div v-if="currentSubCategories.length > 0" class="sub-category-container">
                <div class="sub-category-grid">
                  <div
                    v-for="subCategory in currentSubCategories"
                    :key="subCategory.id"
                    class="sub-category-item"
                    :class="{ selected: isSubCategorySelected(subCategory) }"
                    @click="toggleSubCategory(subCategory)">
                    <div class="sub-category-content">
                      <div class="sub-category-name">{{ subCategory.name }}</div>
                      <div v-if="subCategory.remark" class="sub-category-remark">
                        {{ subCategory.remark }}
                      </div>
                    </div>
                    <q-checkbox
                      :model-value="isSubCategorySelected(subCategory)"
                      color="primary"
                      size="sm"
                      @click.stop
                      @update:model-value="(val) => handleCheckboxChange(subCategory, val)"
                      :key="`checkbox-${selectedMainCategory}-${subCategory.id}-${selectedCategories.length}-${forceUpdate}`" />
                  </div>
                </div>
              </div>
              <div v-else class="empty-state">
                <q-icon name="category" size="32px" class="q-mb-sm" />
                <div>{{ $t('common.selectMainCategoryFirst', '请先选择主分类') }}</div>
              </div>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="between" class="q-pa-sm">
          <div class="text-caption text-grey-6">{{ $t('common.selectedCount', '已选择') }}: {{ selectedCategories.length }} {{ $t('common.items', '项') }}</div>
          <div>
            <q-btn flat size="sm" :label="$t('common.cancel', '取消')" @click="cancelSelection" />
            <q-btn flat size="sm" color="negative" :label="$t('common.clear', '清空')" @click="clearSelection" />
            <q-btn color="primary" size="sm" :label="$t('common.confirm', '确定')" @click="confirmSelection" />
          </div>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import CategoryApi from '~/composables/categoryApi';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  label: {
    type: String,
    default: '选择分类',
  },
  error: {
    type: Boolean,
    default: false,
  },
  errorMessage: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);

const { t } = useI18n();

// 状态管理
const loading = ref(false);
const categories = ref([]);
const selectedCategories = ref([...props.modelValue]);
const selectedMainCategory = ref(null);
const showCategoryDialog = ref(false);
const forceUpdate = ref(0); // 用于强制更新组件
// 计算属性
const selectedCategoriesDisplay = computed(() => {
  if (selectedCategories.value.length === 0) return '';
  if (selectedCategories.value.length === 1) {
    const cat = selectedCategories.value[0];
    return `${cat.mainName} / ${cat.subName}`;
  }
  return `${selectedCategories.value.length} 个分类`;
});

const filteredMainCategories = computed(() => {
  return categories.value;
});

const selectedMainCategoryInfo = computed(() => {
  if (!selectedMainCategory.value) return null;
  return categories.value.find((cat) => cat.id === selectedMainCategory.value);
});

const currentSubCategories = computed(() => {
  if (!selectedMainCategory.value) return [];
  const mainCategory = categories.value.find((cat) => cat.id === selectedMainCategory.value);
  return mainCategory?.children || [];
});

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    selectedCategories.value = [...newValue];
  },
  { immediate: true }
);

// 监听选中值变化 - 移除自动emit，避免循环更新
// watch(selectedCategories, (newValue) => {
//   emit('update:modelValue', newValue)
// }, { deep: true })

// 获取分类列表
const fetchCategories = async () => {
  loading.value = true;
  try {
    const response = await CategoryApi.getList();
    if (response.code === 0 && response.data) {
      categories.value = response.data;
      // 不默认选择主分类，让用户手动选择
    }
  } catch (error) {
    console.error('获取分类列表失败:', error);
    // 可以在这里添加错误提示
  } finally {
    loading.value = false;
  }
};

// 选择主分类
const selectMainCategory = (category) => {
  selectedMainCategory.value = category.id;
};

// 检查子分类是否已选中
const isSubCategorySelected = (subCategory) => {
  return selectedCategories.value.some((cat) => cat.mainId === selectedMainCategory.value && cat.subId === subCategory.id);
};

// 切换子分类选择状态
const toggleSubCategory = (subCategory) => {
  const mainCategory = categories.value.find((cat) => cat.id === selectedMainCategory.value);
  if (!mainCategory) return;

  const categoryInfo = {
    mainId: mainCategory.id,
    mainName: mainCategory.name,
    subId: subCategory.id,
    subName: subCategory.name,
    remark: subCategory.remark,
  };

  const existingIndex = selectedCategories.value.findIndex((cat) => cat.mainId === categoryInfo.mainId && cat.subId === categoryInfo.subId);

  if (existingIndex >= 0) {
    // 如果已存在，则移除
    selectedCategories.value.splice(existingIndex, 1);
  } else {
    // 如果不存在，则添加
    selectedCategories.value.push(categoryInfo);
  }

  // 强制触发响应式更新
  forceUpdate.value++;
  nextTick(() => {
    // 确保视图更新
  });
};

// 处理复选框变化
const handleCheckboxChange = (subCategory, checked) => {
  console.log('Checkbox change:', subCategory.name, 'checked:', checked);
  const isCurrentlySelected = isSubCategorySelected(subCategory);
  console.log('Currently selected:', isCurrentlySelected);

  // 直接根据checked状态来决定操作
  if (checked && !isCurrentlySelected) {
    // 需要添加
    toggleSubCategory(subCategory);
  } else if (!checked && isCurrentlySelected) {
    // 需要移除
    toggleSubCategory(subCategory);
  }
};

// 移除分类
const removeCategory = (category) => {
  console.log('removeCategory called with:', category);
  try {
    const index = selectedCategories.value.findIndex((cat) => cat.mainId === category.mainId && cat.subId === category.subId);
    if (index >= 0) {
      selectedCategories.value.splice(index, 1);
      // 强制更新
      forceUpdate.value++;
      // 立即更新父组件
      emit('update:modelValue', selectedCategories.value);
      console.log('Category removed, new selection:', selectedCategories.value);
    }
  } catch (error) {
    console.error('Error removing category:', error);
  }
};

// 取消选择
const cancelSelection = () => {
  selectedCategories.value = [...props.modelValue];
  showCategoryDialog.value = false;
};

// 清空选择
const clearSelection = () => {
  selectedCategories.value = [];
};

// 打开分类选择对话框
const openCategoryDialog = () => {
  console.log('Opening category dialog...', showCategoryDialog.value);
  // 重新同步选中状态
  selectedCategories.value = [...props.modelValue];
  // 强制更新
  forceUpdate.value++;
  showCategoryDialog.value = true;
  console.log('After setting:', showCategoryDialog.value);
};

// 确认选择
const confirmSelection = () => {
  showCategoryDialog.value = false;
  emit('update:modelValue', selectedCategories.value);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchCategories();
});
</script>

<style lang="scss" scoped>
.category-selector {
  .category-input-wrapper {
    cursor: pointer;
  }

  :deep(.q-field__control) {
    cursor: pointer;
  }
}

.category-dialog {
  .category-content {
    height: 450px;
    display: flex;
    flex-direction: column;
  }

  .category-layout {
    flex: 1;
    display: flex;
    gap: 12px;
    min-height: 0;
  }

  .main-category-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .sub-category-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .category-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    padding: 0 4px;
  }

  .main-category-container {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: white;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .main-category-list {
    flex: 1;
    overflow-y: auto;
    padding: 4px;
  }

  .main-category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    &.active {
      background-color: #e3f2fd;
      color: #1976d2;
      border-left: 3px solid #1976d2;

      .q-icon {
        color: #1976d2;
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .category-item-content {
    flex: 1;
  }

  .category-name {
    font-size: 13px;
    font-weight: 500;
  }

  .sub-category-container {
    flex: 2;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: white;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .sub-category-grid {
    flex: 1;
    overflow-y: auto;
    padding: 6px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 6px;
    align-content: start;
  }

  .sub-category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    min-height: 40px;

    &:hover {
      border-color: #1976d2;
      box-shadow: 0 2px 4px rgba(25, 118, 210, 0.1);
    }

    &.selected {
      border-color: #1976d2;
      background-color: #e3f2fd;
      box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
    }
  }

  .sub-category-content {
    flex: 1;
    min-width: 0;
  }

  .sub-category-name {
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 1px;
    line-height: 1.3;
  }

  .sub-category-remark {
    font-size: 10px;
    color: #666;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
    padding: 40px 20px;
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .close-btn {
    color: #666;

    &:hover {
      color: #333;
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .category-dialog {
    .q-card {
      height: 80vh !important;
    }

    .category-content {
      height: calc(80vh - 120px);

      .category-row {
        flex-direction: column;
        height: auto !important;
      }

      .col-4,
      .col-8 {
        width: 100%;
        max-width: 100%;
      }

      .main-category-list {
        height: 150px;
      }

      .sub-category-grid {
        height: 200px;
      }
    }
  }
}

@media (max-width: 480px) {
  .category-selector {
    :deep(.q-chip) {
      font-size: 0.7rem;
      height: 24px;
    }
  }

  .category-dialog {
    .sub-category-item {
      padding: 6px 8px;

      .sub-category-name {
        font-size: 12px;
      }

      .sub-category-remark {
        font-size: 10px;
      }
    }
  }
}

// 滚动条样式
.main-category-list::-webkit-scrollbar,
.sub-category-grid::-webkit-scrollbar {
  width: 4px;
}

.main-category-list::-webkit-scrollbar-track,
.sub-category-grid::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 2px;
}

.main-category-list::-webkit-scrollbar-thumb,
.sub-category-grid::-webkit-scrollbar-thumb {
  background: #d0d0d0;
  border-radius: 2px;
}

.main-category-list::-webkit-scrollbar-thumb:hover,
.sub-category-grid::-webkit-scrollbar-thumb:hover {
  background: #b0b0b0;
}
</style>
