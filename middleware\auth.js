import { useAuthStore } from '~/store/auth';
import { useUserStore } from '~/store/user';
export default defineNuxtRouteMiddleware(async (to, from) => {
  const redirectPath = useState('redirectPath'); // 用于存储重定向路径
  const authStore = useAuthStore();
  const userStore = useUserStore();

  console.log('to:', to.path, ';form:', from.path);

  // 如果用户未登录
  if (!authStore.isLogin) {
    //如果不是到登录页 并且访问的路径是 /account/ 开头则需要重新登录然后再跳转回去
    // if (to.path !== '/login' && (to.path.startsWith('/account/') || to.path.startsWith('/order'))) {
    //   // 记录当前路径到 redirectPath
    //   redirectPath.value = to.fullPath;
    //   return navigateTo('/login'); // 跳转到登录页
    // }
    // 未登录，跳转到登录页，并携带当前页面路径
    return navigateTo({
      path: '/login',
      query: {
        returnTo: to.fullPath, // 保存用户原本想访问的页面
      },
    });
  } else {
    // 用户已登录，要判断邮箱是否已验证
    // 如果邮箱未验证，且访问的是 /account/ 开头的路径
    if (userStore.userInfo?.status === 2) {
      if (to.path !== '/mail-verify') {
        //获取最新用户信息后，再次检查是否已验证
        await userStore.getUserInfo();
        if (userStore.userInfo?.status === 2 && to.path.startsWith('/account/')) {
          // redirectPath.value = to.fullPath; // 记录当前路径
          return navigateTo('/mail-verify'); // 跳转到邮箱验证页面
        }
      }
    }
  }
});
