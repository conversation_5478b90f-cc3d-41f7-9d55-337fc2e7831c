<template>
  <div class="customer-service-button" :class="{ mobile: isMobile }">
    <q-btn round color="primary" class="service-btn" size="lg" @click="navigateToChat" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
      <q-icon name="headset_mic" size="md" />

      <q-badge v-if="unreadCount > 0" color="negative" floating>
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </q-badge>
    </q-btn>

    <div v-if="showTooltip && !isMobile" class="tooltip">
      {{ $t('help.customerService.title') }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { useResponsive } from '~/composables/useResponsive';
import { useAuthStore } from '~/store/auth';

const router = useRouter();
const authStore = useAuthStore();
const { isMobile } = useResponsive();

const showTooltip = ref(false);
const unreadCount = ref(0);
let checkInterval = null;

// 导航到聊天页面
function navigateToChat() {
  // 检查用户是否已登录
  if (!authStore.isLogin) {
    // 如果未登录，先跳转到登录页面，并设置登录后的重定向地址为聊天页面
    router.push({
      path: '/login',
      query: { returnTo: '/chat' },
    });
    return;
  }

  // 已登录，在新窗口打开聊天页面
  window.open('/chat', '_blank');
}

// 模拟检查未读消息数量
function checkUnreadMessages() {
  // 这里应该调用API获取未读消息数量
  // 现在使用随机数模拟
  if (Math.random() > 0.7) {
    unreadCount.value = Math.floor(Math.random() * 10);
  }
}

onMounted(() => {
  // 每30秒检查一次未读消息
  checkInterval = setInterval(checkUnreadMessages, 30000);
  // 初始检查
  checkUnreadMessages();
});

onBeforeUnmount(() => {
  // 清除定时器
  if (checkInterval) {
    clearInterval(checkInterval);
  }
});
</script>

<style scoped lang="scss">
.customer-service-button {
  position: fixed;
  right: 30px;
  bottom: 100px;
  z-index: 1000;
  display: flex;
  align-items: center;

  &.mobile {
    right: 20px;
    bottom: 80px;
  }

  .service-btn {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
    }
  }

  .tooltip {
    position: absolute;
    right: 60px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    white-space: nowrap;

    &:after {
      content: '';
      position: absolute;
      top: 50%;
      right: -6px;
      transform: translateY(-50%);
      border-width: 6px 0 6px 6px;
      border-style: solid;
      border-color: transparent transparent transparent rgba(0, 0, 0, 0.7);
    }
  }
}
</style>
