const NotifyApi = {
  getPage: (params) => {
    return useClientGet('/system/notify-message/my-page', {
      params,
    });
  },
  updateRead: (params) => {
    return useClientPut('/system/notify-message/update-read', {
      params,
    });
  },
  updateAllRead: () => {
    return useClientPut('/system/notify-message/update-all-read');
  },
  getUnreadList: (params) => {
    return useClientGet('/system/notify-message/get-unread-list', {
      params,
    });
  },
  getList: (params) => {
    return useClientGet('/system/notify-message/get-list', {
      params,
    });
  },
  getUnreadCount: () => {
    return useClientGet('/system/notify-message/get-unread-count');
  },
  batchDelete: (params) => {
    return useClientDelete('/system/notify-message/batch-delete', {
      params,
    });
  },
  deleteAll: () => {
    return useClientDelete('/system/notify-message/delete-all');
  },
};

export default NotifyApi;
