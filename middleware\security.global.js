/**
 * 全局安全中间件
 *
 * 功能：
 * - XSS攻击防护
 * - 敏感页面访问控制
 * - URL参数清理
 * - 路由权限检查
 * - 恶意请求拦截
 */

export default defineNuxtRouteMiddleware((to, from) => {
  // 仅在客户端执行安全检查
  if (process.client) {
    // 1. XSS防护 - 清理URL参数中的潜在XSS代码
    const cleanedQuery = cleanXSSFromQuery(to.query);

    // 如果参数被清理，重定向到清理后的URL
    if (JSON.stringify(cleanedQuery) !== JSON.stringify(to.query)) {
      console.warn('检测到潜在的XSS攻击，已清理URL参数');
      return navigateTo({
        path: to.path,
        query: cleanedQuery,
      });
    }

    // 2. 敏感页面访问控制
    if (isProtectedRoute(to.path)) {
      const isAuthenticated = checkAuthentication();

      if (!isAuthenticated) {
        console.log('未登录用户尝试访问受保护页面:', to.path);

        // 保存原始访问路径，登录后跳转回来
        const returnTo = encodeURIComponent(to.fullPath);
        return navigateTo(`/login?returnTo=${returnTo}`);
      }
    }

    // 3. 管理员页面权限检查
    if (isAdminRoute(to.path)) {
      const hasAdminPermission = checkAdminPermission();

      if (!hasAdminPermission) {
        console.warn('非管理员用户尝试访问管理页面:', to.path);

        // 显示错误提示
        useNuxtApp().$showNotify({
          msg: '您没有权限访问此页面',
          type: 'negative',
        });

        // 重定向到首页
        return navigateTo('/');
      }
    }

    // 4. 频率限制检查（防止恶意刷新）
    if (shouldCheckRateLimit(to.path)) {
      const isRateLimited = checkRateLimit(to.path);

      if (isRateLimited) {
        console.warn('检测到频繁访问:', to.path);

        useNuxtApp().$showNotify({
          msg: '访问过于频繁，请稍后再试',
          type: 'warning',
        });

        return abortNavigation();
      }
    }

    // 5. 记录页面访问日志（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log('页面访问:', {
        from: from?.path,
        to: to.path,
        query: to.query,
        timestamp: new Date().toISOString(),
      });
    }
  }
});

/**
 * 清理查询参数中的XSS代码
 */
function cleanXSSFromQuery(query) {
  const cleanedQuery = {};

  for (const [key, value] of Object.entries(query)) {
    if (typeof value === 'string') {
      // 移除潜在的脚本标签和事件处理器
      cleanedQuery[key] = value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .replace(/data:text\/html/gi, '')
        .replace(/vbscript:/gi, '');
    } else {
      cleanedQuery[key] = value;
    }
  }

  return cleanedQuery;
}

/**
 * 检查是否为受保护的路由
 */
function isProtectedRoute(path) {
  const protectedRoutes = ['/account', '/order', '/cart', '/checkout', '/pay', '/diy-order'];

  return protectedRoutes.some((route) => path.startsWith(route));
}

/**
 * 检查是否为管理员路由
 */
function isAdminRoute(path) {
  const adminRoutes = ['/admin', '/management'];

  return adminRoutes.some((route) => path.startsWith(route));
}

/**
 * 检查用户认证状态
 */
function checkAuthentication() {
  try {
    const token = useCookie('token').value;

    if (!token) {
      return false;
    }

    // 简单的token格式验证
    if (typeof token !== 'string' || token.length < 10) {
      return false;
    }

    // 可以在这里添加更复杂的token验证逻辑
    // 比如检查token是否过期等

    return true;
  } catch (error) {
    console.error('认证检查失败:', error);
    return false;
  }
}

/**
 * 检查管理员权限
 */
function checkAdminPermission() {
  try {
    // 这里应该检查用户的角色或权限
    // 目前简化为检查是否有特定的cookie或localStorage值

    const userRole = useCookie('userRole').value;
    return userRole === 'admin' || userRole === 'super_admin';
  } catch (error) {
    console.error('权限检查失败:', error);
    return false;
  }
}

/**
 * 检查是否需要进行频率限制
 */
function shouldCheckRateLimit(path) {
  // 对API路由和敏感操作进行频率限制
  const rateLimitedPaths = ['/api', '/login', '/register', '/forgot-password'];

  return rateLimitedPaths.some((route) => path.startsWith(route));
}

/**
 * 检查访问频率限制
 */
function checkRateLimit(path) {
  try {
    const now = Date.now();
    const windowMs = 60 * 1000; // 1分钟窗口
    const maxRequests = 30; // 最大请求数

    // 从localStorage获取访问记录
    const storageKey = `rate_limit_${path}`;
    const accessLog = JSON.parse(localStorage.getItem(storageKey) || '[]');

    // 清理过期记录
    const validAccess = accessLog.filter((timestamp) => now - timestamp < windowMs);

    // 检查是否超过限制
    if (validAccess.length >= maxRequests) {
      return true;
    }

    // 记录当前访问
    validAccess.push(now);
    localStorage.setItem(storageKey, JSON.stringify(validAccess));

    return false;
  } catch (error) {
    console.error('频率限制检查失败:', error);
    return false;
  }
}

/**
 * 清理过期的访问记录（定期清理）
 */
if (process.client) {
  // 每5分钟清理一次过期记录
  setInterval(() => {
    try {
      const now = Date.now();
      const windowMs = 60 * 1000;

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);

        if (key && key.startsWith('rate_limit_')) {
          const accessLog = JSON.parse(localStorage.getItem(key) || '[]');
          const validAccess = accessLog.filter((timestamp) => now - timestamp < windowMs);

          if (validAccess.length === 0) {
            localStorage.removeItem(key);
          } else {
            localStorage.setItem(key, JSON.stringify(validAccess));
          }
        }
      }
    } catch (error) {
      console.error('清理访问记录失败:', error);
    }
  }, 5 * 60 * 1000);
}
