import { date } from 'quasar';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration'; // 引入 duration 插件

dayjs.extend(duration); // 加载插件
// 格式化日期时间
export function formatDateTime(timestamp) {
  return date.formatDate(timestamp, 'YYYY-MM-DD HH:mm:ss');
}

// 格式化日期范围
export function formatDateRange(from, to) {
  // 即使日期相同也显示完整的日期范围格式
  return `${from} 至 ${to}`;
}

/**
 * 倒计时
 * @param toTime   截止时间
 * @param fromTime 起始时间，默认当前时间
 * @return {{s: string, ms: number, h: string, m: string}} 持续时间
 */
export function useDurationTime(toTime, fromTime = '') {
  toTime = getDayjsTime(toTime);
  if (fromTime === '') {
    fromTime = dayjs();
  }
  let duration = ref(toTime - fromTime);
  if (duration.value > 0) {
    setTimeout(() => {
      if (duration.value > 0) {
        duration.value -= 1000;
      }
    }, 1000);
  }

  let durationTime = dayjs.duration(duration.value);
  return {
    h: (durationTime.months() * 30 * 24 + durationTime.days() * 24 + durationTime.hours()).toString().padStart(2, '0'),
    m: durationTime.minutes().toString().padStart(2, '0'),
    s: durationTime.seconds().toString().padStart(2, '0'),
    ms: durationTime.$ms,
  };
}

/**
 * 转换为 Dayjs
 * @param {any} time 时间
 * @return {dayjs.Dayjs}
 */
function getDayjsTime(time) {
  time = time.toString();
  if (time.indexOf('-') > 0) {
    // 'date'
    return dayjs(time);
  }
  if (time.length > 10) {
    // 'timestamp'
    return dayjs(parseInt(time));
  }
  if (time.length === 10) {
    // 'unixTime'
    return dayjs.unix(parseInt(time));
  }
}

/**
 * 将时间戳转换为当地时间字符串
 * @param {number} timestamp - 时间戳，单位为毫秒
 * @returns {string} 格式化的当地时间
 */
export function convertTimestampToLocalTime(timestamp) {
  // 创建一个 Date 对象
  const date = new Date(timestamp);

  // 使用 toLocaleString 方法将时间格式化为当地时间
  return date.toLocaleString();
}

/**
 * 将时间戳格式化为西方常用日期格式 (MM/DD/YYYY)
 * @param {number} timestamp - 时间戳，单位为毫秒
 * @param {string} format - 日期格式，默认为 'MM/DD/YYYY'
 * @returns {string} 格式化的日期字符串
 */
export function formatTimestampToWesternDate(timestamp, format = 'MM/DD/YYYY') {
  if (!timestamp) return '';

  return dayjs(timestamp).format(format);
}

/**
 * 将时间戳格式化为自定义日期格式
 * @param {number} timestamp - 时间戳，单位为毫秒
 * @param {string} format - 日期格式，使用 dayjs 格式
 * @returns {string} 格式化的日期字符串
 *
 * 常用格式：
 * - 'MM/DD/YYYY' - 美式日期 (例如: 05/24/2024)
 * - 'DD/MM/YYYY' - 欧式日期 (例如: 24/05/2024)
 * - 'YYYY-MM-DD' - ISO 日期 (例如: 2024-05-24)
 * - 'MMMM D, YYYY' - 长日期 (例如: May 24, 2024)
 * - 'MMM D, YYYY' - 短月份日期 (例如: May 24, 2024)
 * - 'DD MMM YYYY' - 欧洲风格 (例如: 24 May 2024)
 */
export function formatDate(timestamp, format) {
  if (!timestamp) return '';

  return dayjs(timestamp).format(format);
}

// 格式化短日期时间
export function formatShortDateTime(timestamp) {
  if (!timestamp) return '';
  const now = new Date();
  const msgDate = new Date(timestamp);
  const diffDays = Math.floor((now - msgDate) / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    // 今天，只显示时间
    return date.formatDate(msgDate, 'HH:mm');
  } else if (diffDays === 1) {
    // 昨天
    return '昨天';
  } else if (diffDays < 7) {
    // 一周内，显示星期
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    return '周' + weekdays[msgDate.getDay()];
  } else {
    // 超过一周，显示月日
    return date.formatDate(msgDate, 'MM-DD');
  }
}
